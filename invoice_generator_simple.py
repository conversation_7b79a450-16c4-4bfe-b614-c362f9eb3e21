import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
import config

class SimpleInvoiceGenerator:
    def __init__(self):
        self.ensure_invoices_folder()
        
    def ensure_invoices_folder(self):
        """إنشاء مجلد الفواتير إذا لم يكن موجوداً"""
        if not os.path.exists(config.INVOICES_FOLDER):
            os.makedirs(config.INVOICES_FOLDER)
    
    def get_next_invoice_number(self):
        """الحصول على رقم الفاتورة التالي"""
        invoice_files = [f for f in os.listdir(config.INVOICES_FOLDER) if f.startswith('Invoice-') and f.endswith('.pdf')]
        if not invoice_files:
            return 1
        
        numbers = []
        for filename in invoice_files:
            try:
                num = int(filename.split('-')[1].split('.')[0])
                numbers.append(num)
            except:
                continue
        
        return max(numbers) + 1 if numbers else 1
    
    def generate_invoice(self, items, customer_name="", notes=""):
        """إنشاء الفاتورة"""
        invoice_number = self.get_next_invoice_number()
        filename = f"Invoice-{invoice_number:04d}.pdf"
        filepath = os.path.join(config.INVOICES_FOLDER, filename)
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)
        
        # قائمة العناصر
        story = []
        
        # إضافة الشعار والعنوان
        story.extend(self.create_header(invoice_number))
        
        # معلومات العميل
        story.extend(self.create_customer_info(customer_name))
        
        # جدول المنتجات
        story.extend(self.create_items_table(items))
        
        # المجموع والضريبة
        story.extend(self.create_totals(items))
        
        # الملاحظات
        if notes:
            story.extend(self.create_notes(notes))
        
        # التذييل
        story.extend(self.create_footer())
        
        # بناء المستند
        doc.build(story)
        
        return filepath
    
    def create_header(self, invoice_number):
        """إنشاء رأس الفاتورة"""
        elements = []
        styles = getSampleStyleSheet()
        
        # شعار الشركة
        if os.path.exists(config.LOGO_PATH):
            try:
                logo = Image(config.LOGO_PATH, width=2*inch, height=1*inch)
                elements.append(logo)
            except:
                pass
        
        # اسم الشركة
        company_style = ParagraphStyle(
            'CompanyName',
            parent=styles['Heading1'],
            fontSize=24,
            textColor=colors.HexColor(config.PRIMARY_COLOR),
            alignment=TA_CENTER,
            spaceAfter=12
        )
        elements.append(Paragraph(config.COMPANY_NAME, company_style))
        
        # عنوان الفاتورة
        invoice_style = ParagraphStyle(
            'InvoiceTitle',
            parent=styles['Heading2'],
            fontSize=18,
            alignment=TA_CENTER,
            spaceAfter=20,
            textColor=colors.HexColor(config.PRIMARY_COLOR)
        )
        elements.append(Paragraph("INVOICE", invoice_style))
        
        # معلومات الفاتورة
        invoice_info = [
            ['Invoice Number:', f'{invoice_number:04d}'],
            ['Date:', datetime.now().strftime('%Y-%m-%d')],
            ['Time:', datetime.now().strftime('%H:%M')]
        ]
        
        info_table = Table(invoice_info, colWidths=[3*inch, 2*inch])
        info_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        elements.append(info_table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_customer_info(self, customer_name):
        """معلومات العميل"""
        elements = []
        if customer_name:
            customer_style = ParagraphStyle(
                'Customer',
                fontSize=12,
                spaceAfter=10
            )
            elements.append(Paragraph(f"<b>Customer:</b> {customer_name}", customer_style))
            elements.append(Spacer(1, 10))
        
        return elements
    
    def create_items_table(self, items):
        """إنشاء جدول المنتجات"""
        elements = []
        
        # رأس الجدول
        headers = ['Item/Service', 'Quantity', 'Unit Price', 'Total']
        
        # بيانات الجدول
        table_data = [headers]
        
        for item in items:
            name = item['name']
            quantity = item['quantity']
            price = float(item['price'])
            total = quantity * price
            
            row = [
                name,
                str(quantity),
                f"{price:.2f} {config.CURRENCY_SYMBOL}",
                f"{total:.2f} {config.CURRENCY_SYMBOL}"
            ]
            table_data.append(row)
        
        # إنشاء الجدول
        table = Table(table_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
        table.setStyle(TableStyle([
            # رأس الجدول
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor(config.PRIMARY_COLOR)),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            
            # محتوى الجدول
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.beige, colors.white]),
            
            # حدود الجدول
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_totals(self, items):
        """حساب المجاميع والضريبة"""
        elements = []
        
        # حساب المجموع الفرعي
        subtotal = sum(item['quantity'] * float(item['price']) for item in items)
        tax_amount = subtotal * config.TAX_RATE
        total = subtotal + tax_amount
        
        # جدول المجاميع
        totals_data = [
            ['Subtotal:', f"{subtotal:.2f} {config.CURRENCY_SYMBOL}"],
            [f'VAT ({config.TAX_RATE*100:.0f}%):', f"{tax_amount:.2f} {config.CURRENCY_SYMBOL}"],
            ['Total:', f"{total:.2f} {config.CURRENCY_SYMBOL}"]
        ]
        
        totals_table = Table(totals_data, colWidths=[3*inch, 2*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            
            # تمييز الإجمالي النهائي
            ('BACKGROUND', (0, -1), (-1, -1), colors.HexColor(config.PRIMARY_COLOR)),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.whitesmoke),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
        ]))
        
        elements.append(totals_table)
        elements.append(Spacer(1, 30))
        
        return elements
    
    def create_notes(self, notes):
        """إضافة الملاحظات"""
        elements = []
        
        notes_style = ParagraphStyle(
            'Notes',
            fontSize=10,
            spaceAfter=10
        )
        elements.append(Paragraph(f"<b>Notes:</b><br/>{notes}", notes_style))
        elements.append(Spacer(1, 20))
        
        return elements
    
    def create_footer(self):
        """تذييل الفاتورة"""
        elements = []
        
        # معلومات الشركة
        footer_style = ParagraphStyle(
            'Footer',
            fontSize=9,
            alignment=TA_CENTER,
            textColor=colors.grey
        )
        
        footer_text = f"""
        <b>{config.COMPANY_NAME}</b><br/>
        Saudi Arabia<br/>
        Phone: {config.COMPANY_PHONE} | Email: {config.COMPANY_EMAIL}<br/>
        Website: {config.COMPANY_WEBSITE}<br/>
        <br/>
        Thank you for your business!
        """
        
        elements.append(Spacer(1, 30))
        elements.append(Paragraph(footer_text, footer_style))
        
        return elements
