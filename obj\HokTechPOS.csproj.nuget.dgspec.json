{"format": 1, "restore": {"C:\\xampp\\htdocs\\inovicetech\\HokTechPOS.csproj": {}}, "projects": {"C:\\xampp\\htdocs\\inovicetech\\HokTechPOS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\xampp\\htdocs\\inovicetech\\HokTechPOS.csproj", "projectName": "HokTechPOS", "projectPath": "C:\\xampp\\htdocs\\inovicetech\\HokTechPOS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\xampp\\htdocs\\inovicetech\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\porgramcode2\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"System.Drawing.Common": {"target": "Package", "version": "[7.0.0, )"}, "iTextSharp": {"target": "Package", "version": "[5.5.13.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.102\\RuntimeIdentifierGraph.json"}}}}}