using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Threading;
using HtmlAgilityPack;
using SitemapGenerator.Models;
using System.Text.RegularExpressions;
using System.IO;

namespace SitemapGenerator.Services
{
    /// <summary>
    /// Web crawler service for discovering URLs on a website
    /// </summary>
    public class WebCrawler
    {
        private readonly HttpClient _httpClient;
        private readonly SitemapSettings _settings;
        private readonly HashSet<string> _visitedUrls;
        private readonly HashSet<string> _discoveredUrls;
        private readonly Queue<CrawlItem> _crawlQueue;
        private readonly List<SitemapUrl> _sitemapUrls;
        private readonly CancellationTokenSource _cancellationTokenSource;

        public event EventHandler<CrawlProgressEventArgs>? ProgressChanged;
        public event EventHandler<string>? StatusChanged;
        public event EventHandler<SitemapUrl>? UrlDiscovered;

        public bool IsCrawling { get; private set; }
        public int TotalUrlsDiscovered => _discoveredUrls.Count;
        public int TotalUrlsProcessed => _visitedUrls.Count;

        public WebCrawler(SitemapSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", _settings.CrawlSettings.UserAgent);
            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.CrawlSettings.TimeoutSeconds);
            
            _visitedUrls = new HashSet<string>();
            _discoveredUrls = new HashSet<string>();
            _crawlQueue = new Queue<CrawlItem>();
            _sitemapUrls = new List<SitemapUrl>();
            _cancellationTokenSource = new CancellationTokenSource();
        }

        /// <summary>
        /// Start crawling the website
        /// </summary>
        public async Task<List<SitemapUrl>> CrawlAsync(string startUrl)
        {
            if (IsCrawling)
                throw new InvalidOperationException("Crawler is already running");

            try
            {
                IsCrawling = true;
                _visitedUrls.Clear();
                _discoveredUrls.Clear();
                _crawlQueue.Clear();
                _sitemapUrls.Clear();

                OnStatusChanged("Starting website crawl...");

                // Normalize start URL
                var baseUri = new Uri(_settings.BaseUrl);
                var normalizedStartUrl = NormalizeUrl(startUrl, baseUri);

                // Add start URL to queue
                _crawlQueue.Enqueue(new CrawlItem(normalizedStartUrl, 0));
                _discoveredUrls.Add(normalizedStartUrl);

                // Process crawl queue
                while (_crawlQueue.Count > 0 && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var currentItem = _crawlQueue.Dequeue();
                    
                    if (_visitedUrls.Count >= _settings.CrawlSettings.MaxPages)
                    {
                        OnStatusChanged($"Reached maximum page limit ({_settings.CrawlSettings.MaxPages})");
                        break;
                    }

                    if (currentItem.Depth > _settings.CrawlSettings.MaxDepth)
                        continue;

                    await ProcessUrlAsync(currentItem);

                    // Delay between requests
                    if (_settings.CrawlSettings.DelayBetweenRequests > 0)
                    {
                        await Task.Delay(_settings.CrawlSettings.DelayBetweenRequests, _cancellationTokenSource.Token);
                    }

                    // Report progress
                    OnProgressChanged(new CrawlProgressEventArgs
                    {
                        TotalDiscovered = _discoveredUrls.Count,
                        TotalProcessed = _visitedUrls.Count,
                        CurrentUrl = currentItem.Url
                    });
                }

                OnStatusChanged($"Crawl completed. Found {_sitemapUrls.Count} URLs.");
                return _sitemapUrls.ToList();
            }
            catch (OperationCanceledException)
            {
                OnStatusChanged("Crawl was cancelled.");
                return _sitemapUrls.ToList();
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Crawl error: {ex.Message}");
                throw;
            }
            finally
            {
                IsCrawling = false;
            }
        }

        /// <summary>
        /// Stop the crawling process
        /// </summary>
        public void Stop()
        {
            _cancellationTokenSource.Cancel();
            OnStatusChanged("Stopping crawl...");
        }

        private async Task ProcessUrlAsync(CrawlItem item)
        {
            if (_visitedUrls.Contains(item.Url))
                return;

            _visitedUrls.Add(item.Url);

            try
            {
                OnStatusChanged($"Crawling: {item.Url}");

                var response = await _httpClient.GetAsync(item.Url, _cancellationTokenSource.Token);
                
                if (!response.IsSuccessStatusCode)
                {
                    OnStatusChanged($"Failed to fetch {item.Url}: {response.StatusCode}");
                    return;
                }

                var contentType = response.Content.Headers.ContentType?.MediaType ?? "";
                var content = await response.Content.ReadAsStringAsync();

                // Create sitemap URL entry
                var sitemapUrl = CreateSitemapUrl(item.Url, response, content);
                _sitemapUrls.Add(sitemapUrl);
                OnUrlDiscovered(sitemapUrl);

                // Parse HTML content for links
                if (contentType.Contains("text/html") && item.Depth < _settings.CrawlSettings.MaxDepth)
                {
                    await ExtractLinksAsync(content, item.Url, item.Depth + 1);
                }
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Error processing {item.Url}: {ex.Message}");
            }
        }

        private SitemapUrl CreateSitemapUrl(string url, HttpResponseMessage response, string content)
        {
            var sitemapUrl = new SitemapUrl
            {
                Url = url,
                LastModified = response.Content.Headers.LastModified?.DateTime ?? DateTime.Now,
                ChangeFrequency = _settings.DefaultChangeFrequency,
                Priority = CalculatePriority(url),
                Language = _settings.DefaultLanguage
            };

            // Extract SEO information from HTML
            if (content.Contains("<html", StringComparison.OrdinalIgnoreCase))
            {
                ExtractSeoInfo(sitemapUrl, content);
            }

            return sitemapUrl;
        }

        private void ExtractSeoInfo(SitemapUrl sitemapUrl, string htmlContent)
        {
            try
            {
                var doc = new HtmlAgilityPack.HtmlDocument();
                doc.LoadHtml(htmlContent);

                // Extract title
                var titleNode = doc.DocumentNode.SelectSingleNode("//title");
                if (titleNode != null)
                {
                    sitemapUrl.Title = titleNode.InnerText.Trim();
                }

                // Extract meta description
                var descriptionNode = doc.DocumentNode.SelectSingleNode("//meta[@name='description']");
                if (descriptionNode != null)
                {
                    sitemapUrl.Description = descriptionNode.GetAttributeValue("content", "").Trim();
                }

                // Extract meta keywords
                var keywordsNode = doc.DocumentNode.SelectSingleNode("//meta[@name='keywords']");
                if (keywordsNode != null)
                {
                    sitemapUrl.Keywords = keywordsNode.GetAttributeValue("content", "").Trim();
                }

                // Extract language
                var langNode = doc.DocumentNode.SelectSingleNode("//html[@lang]");
                if (langNode != null)
                {
                    var lang = langNode.GetAttributeValue("lang", "");
                    if (!string.IsNullOrEmpty(lang))
                    {
                        sitemapUrl.Language = lang.Split('-')[0]; // Take primary language code
                    }
                }

                // Extract images
                var imageNodes = doc.DocumentNode.SelectNodes("//img[@src]");
                if (imageNodes != null)
                {
                    foreach (var imgNode in imageNodes.Take(10)) // Limit to 10 images per page
                    {
                        var src = imgNode.GetAttributeValue("src", "");
                        if (!string.IsNullOrEmpty(src))
                        {
                            var imageUrl = NormalizeUrl(src, new Uri(sitemapUrl.Url));
                            sitemapUrl.Images.Add(new ImageInfo
                            {
                                Url = imageUrl,
                                Title = imgNode.GetAttributeValue("title", ""),
                                Caption = imgNode.GetAttributeValue("alt", "")
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting SEO info: {ex.Message}");
            }
        }

        private Task ExtractLinksAsync(string htmlContent, string baseUrl, int depth)
        {
            try
            {
                var doc = new HtmlAgilityPack.HtmlDocument();
                doc.LoadHtml(htmlContent);

                var linkNodes = doc.DocumentNode.SelectNodes("//a[@href]");
                if (linkNodes == null) return Task.CompletedTask;

                var baseUri = new Uri(baseUrl);

                foreach (var linkNode in linkNodes)
                {
                    var href = linkNode.GetAttributeValue("href", "");
                    if (string.IsNullOrEmpty(href)) continue;

                    var normalizedUrl = NormalizeUrl(href, baseUri);
                    
                    if (ShouldCrawlUrl(normalizedUrl) && !_discoveredUrls.Contains(normalizedUrl))
                    {
                        _discoveredUrls.Add(normalizedUrl);
                        _crawlQueue.Enqueue(new CrawlItem(normalizedUrl, depth));
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting links: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        private string NormalizeUrl(string url, Uri baseUri)
        {
            try
            {
                if (Uri.TryCreate(baseUri, url, out var absoluteUri))
                {
                    return absoluteUri.ToString().Split('#')[0]; // Remove fragment
                }
            }
            catch
            {
                // Ignore invalid URLs
            }
            
            return url;
        }

        private bool ShouldCrawlUrl(string url)
        {
            try
            {
                var uri = new Uri(url);
                
                // Only crawl URLs from the same domain (unless external links are allowed)
                if (!_settings.CrawlSettings.FollowExternalLinks)
                {
                    var baseUri = new Uri(_settings.BaseUrl);
                    if (!uri.Host.Equals(baseUri.Host, StringComparison.OrdinalIgnoreCase))
                        return false;
                }

                // Check file extension
                var extension = Path.GetExtension(uri.AbsolutePath).ToLowerInvariant();
                if (!string.IsNullOrEmpty(extension) && 
                    !_settings.CrawlSettings.IncludeExtensions.Contains(extension))
                    return false;

                // Check exclude patterns
                foreach (var pattern in _settings.CrawlSettings.ExcludePatterns)
                {
                    if (url.Contains(pattern, StringComparison.OrdinalIgnoreCase))
                        return false;
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        private double CalculatePriority(string url)
        {
            var uri = new Uri(url);
            var path = uri.AbsolutePath.ToLowerInvariant();

            // Home page gets highest priority
            if (path == "/" || path == "/index.html" || path == "/index.htm")
                return 1.0;

            // Important pages get high priority
            if (path.Contains("/about") || path.Contains("/contact") || path.Contains("/services"))
                return 0.8;

            // Category/section pages get medium priority
            var segments = path.Split('/').Where(s => !string.IsNullOrEmpty(s)).ToArray();
            if (segments.Length <= 2)
                return 0.7;

            // Deep pages get lower priority
            return Math.Max(0.1, 0.8 - (segments.Length * 0.1));
        }

        protected virtual void OnProgressChanged(CrawlProgressEventArgs e)
        {
            ProgressChanged?.Invoke(this, e);
        }

        protected virtual void OnStatusChanged(string status)
        {
            StatusChanged?.Invoke(this, status);
        }

        protected virtual void OnUrlDiscovered(SitemapUrl url)
        {
            UrlDiscovered?.Invoke(this, url);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
            _cancellationTokenSource?.Dispose();
        }
    }

    public class CrawlItem
    {
        public string Url { get; }
        public int Depth { get; }

        public CrawlItem(string url, int depth)
        {
            Url = url;
            Depth = depth;
        }
    }

    public class CrawlProgressEventArgs : EventArgs
    {
        public int TotalDiscovered { get; set; }
        public int TotalProcessed { get; set; }
        public string CurrentUrl { get; set; } = string.Empty;
    }
}
