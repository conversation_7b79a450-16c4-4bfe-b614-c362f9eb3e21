@echo off
echo ========================================
echo Testing HokTech POS C# System
echo اختبار نظام نقاط البيع C# - هوك تك
echo ========================================
echo.

echo 🧪 Running C# POS System Test...
echo 🧪 تشغيل اختبار نظام نقاط البيع C#...
echo.

echo ✅ Checking .NET installation...
echo ✅ فحص تثبيت .NET...
dotnet --version
if %errorlevel% neq 0 (
    echo ❌ .NET not found!
    echo ❌ .NET غير موجود!
    pause
    exit /b 1
)

echo.
echo ✅ Building project...
echo ✅ بناء المشروع...
dotnet build HokTechPOS.csproj --configuration Release --verbosity quiet
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo ❌ فشل البناء!
    pause
    exit /b 1
)

echo.
echo 🎉 Build successful!
echo 🎉 البناء نجح!
echo.
echo 📋 C# POS System Features:
echo 📋 ميزات نظام نقاط البيع C#:
echo    ✅ Windows Forms UI - واجهة Windows Forms
echo    ✅ Professional Design - تصميم احترافي
echo    ✅ PDF Invoice Generation - إنشاء فواتير PDF
echo    ✅ No Arabic Text Issues - لا مشاكل في النص العربي
echo    ✅ Fast Performance - أداء سريع
echo    ✅ Easy to Use - سهل الاستخدام
echo.
echo 🚀 Ready to run! Starting application...
echo 🚀 جاهز للتشغيل! بدء التطبيق...
echo.

dotnet run --project HokTechPOS.csproj --configuration Release

echo.
echo 📊 Test completed!
echo 📊 اكتمل الاختبار!
echo.
echo 💡 To run again, use:
echo 💡 للتشغيل مرة أخرى، استخدم:
echo    build_and_run.bat
echo    or / أو
echo    dotnet run --project HokTechPOS.csproj
echo.
pause
