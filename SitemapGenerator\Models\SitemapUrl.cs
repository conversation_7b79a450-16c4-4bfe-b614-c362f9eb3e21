using System;
using System.ComponentModel;

namespace SitemapGenerator.Models
{
    /// <summary>
    /// Represents a URL entry in the sitemap with all SEO properties
    /// </summary>
    public class SitemapUrl : INotifyPropertyChanged
    {
        private string _url = string.Empty;
        private DateTime _lastModified = DateTime.Now;
        private ChangeFrequency _changeFrequency = ChangeFrequency.Weekly;
        private double _priority = 0.5;
        private bool _isIncluded = true;
        private string _title = string.Empty;
        private string _description = string.Empty;
        private string _keywords = string.Empty;
        private string _language = "en";
        private UrlType _urlType = UrlType.Page;

        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// The URL of the page
        /// </summary>
        public string Url
        {
            get => _url;
            set
            {
                if (_url != value)
                {
                    _url = value;
                    OnPropertyChanged(nameof(Url));
                }
            }
        }

        /// <summary>
        /// Last modification date of the page
        /// </summary>
        public DateTime LastModified
        {
            get => _lastModified;
            set
            {
                if (_lastModified != value)
                {
                    _lastModified = value;
                    OnPropertyChanged(nameof(LastModified));
                }
            }
        }

        /// <summary>
        /// How frequently the page is likely to change
        /// </summary>
        public ChangeFrequency ChangeFrequency
        {
            get => _changeFrequency;
            set
            {
                if (_changeFrequency != value)
                {
                    _changeFrequency = value;
                    OnPropertyChanged(nameof(ChangeFrequency));
                }
            }
        }

        /// <summary>
        /// Priority of this URL relative to other URLs (0.0 to 1.0)
        /// </summary>
        public double Priority
        {
            get => _priority;
            set
            {
                if (Math.Abs(_priority - value) > 0.001)
                {
                    _priority = Math.Max(0.0, Math.Min(1.0, value));
                    OnPropertyChanged(nameof(Priority));
                }
            }
        }

        /// <summary>
        /// Whether this URL should be included in the sitemap
        /// </summary>
        public bool IsIncluded
        {
            get => _isIncluded;
            set
            {
                if (_isIncluded != value)
                {
                    _isIncluded = value;
                    OnPropertyChanged(nameof(IsIncluded));
                }
            }
        }

        /// <summary>
        /// Page title for SEO
        /// </summary>
        public string Title
        {
            get => _title;
            set
            {
                if (_title != value)
                {
                    _title = value ?? string.Empty;
                    OnPropertyChanged(nameof(Title));
                }
            }
        }

        /// <summary>
        /// Page description for SEO
        /// </summary>
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value ?? string.Empty;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        /// <summary>
        /// Keywords for SEO
        /// </summary>
        public string Keywords
        {
            get => _keywords;
            set
            {
                if (_keywords != value)
                {
                    _keywords = value ?? string.Empty;
                    OnPropertyChanged(nameof(Keywords));
                }
            }
        }

        /// <summary>
        /// Language code (e.g., "en", "ar")
        /// </summary>
        public string Language
        {
            get => _language;
            set
            {
                if (_language != value)
                {
                    _language = value ?? "en";
                    OnPropertyChanged(nameof(Language));
                }
            }
        }

        /// <summary>
        /// Type of URL (Page, Image, Video, News)
        /// </summary>
        public UrlType UrlType
        {
            get => _urlType;
            set
            {
                if (_urlType != value)
                {
                    _urlType = value;
                    OnPropertyChanged(nameof(UrlType));
                }
            }
        }

        /// <summary>
        /// Additional images associated with this URL
        /// </summary>
        public List<ImageInfo> Images { get; set; } = new List<ImageInfo>();

        /// <summary>
        /// Video information if this is a video URL
        /// </summary>
        public VideoInfo? Video { get; set; }

        /// <summary>
        /// News information if this is a news URL
        /// </summary>
        public NewsInfo? News { get; set; }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override string ToString()
        {
            return $"{Url} (Priority: {Priority:F1}, Frequency: {ChangeFrequency})";
        }
    }

    /// <summary>
    /// How frequently the page is likely to change
    /// </summary>
    public enum ChangeFrequency
    {
        Always,
        Hourly,
        Daily,
        Weekly,
        Monthly,
        Yearly,
        Never
    }

    /// <summary>
    /// Type of URL content
    /// </summary>
    public enum UrlType
    {
        Page,
        Image,
        Video,
        News
    }

    /// <summary>
    /// Image information for image sitemaps
    /// </summary>
    public class ImageInfo
    {
        public string Url { get; set; } = string.Empty;
        public string Caption { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string License { get; set; } = string.Empty;
    }

    /// <summary>
    /// Video information for video sitemaps
    /// </summary>
    public class VideoInfo
    {
        public string ThumbnailUrl { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string ContentUrl { get; set; } = string.Empty;
        public string PlayerUrl { get; set; } = string.Empty;
        public int Duration { get; set; } // in seconds
        public DateTime PublicationDate { get; set; } = DateTime.Now;
        public string Category { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new List<string>();
    }

    /// <summary>
    /// News information for news sitemaps
    /// </summary>
    public class NewsInfo
    {
        public string PublicationName { get; set; } = string.Empty;
        public string Language { get; set; } = "en";
        public DateTime PublicationDate { get; set; } = DateTime.Now;
        public string Title { get; set; } = string.Empty;
        public string Keywords { get; set; } = string.Empty;
        public List<string> Genres { get; set; } = new List<string>();
    }
}
