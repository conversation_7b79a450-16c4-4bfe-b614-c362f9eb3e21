using System;

namespace HokTechPOS.Models
{
    public class Product
    {
        public string Name { get; set; } = string.Empty;
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public decimal Total => Quantity * Price;
        
        public Product()
        {
        }
        
        public Product(string name, int quantity, decimal price)
        {
            Name = name;
            Quantity = quantity;
            Price = price;
        }
        
        public override string ToString()
        {
            return $"{Name} - Qty: {Quantity} - Price: {Price:C} - Total: {Total:C}";
        }
    }
}
