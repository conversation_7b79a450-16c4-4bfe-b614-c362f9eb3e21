@echo off
echo ========================================
echo HokTech POS System - C# Version
echo نظام نقاط البيع - هوك تك - نسخة سي شارب
echo ========================================
echo.

echo Checking .NET installation...
echo فحص تثبيت .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 6.0 or later is required!
    echo خطأ: يتطلب .NET 6.0 أو أحدث!
    echo.
    echo Please download and install .NET from:
    echo يرجى تحميل وتثبيت .NET من:
    echo https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET found! Building application...
echo تم العثور على .NET! بناء التطبيق...
echo.

dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages!
    echo خطأ: فشل في استعادة الحزم!
    pause
    exit /b 1
)

dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    echo خطأ: فشل البناء!
    pause
    exit /b 1
)

echo.
echo Build successful! Starting application...
echo البناء نجح! بدء التطبيق...
echo.

dotnet run --configuration Release

echo.
echo Application closed.
echo تم إغلاق التطبيق.
pause
