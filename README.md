# HokTech POS System - نظام نقاط البيع

أداة POS بسيطة وسهلة الاستخدام لإنشاء فواتير PDF احترافية لشركة HokTech.

## الميزات / Features

### العربية:
- واجهة مستخدم بسيطة وسهلة
- إضافة منتجات/خدمات متعددة
- حساب الضريبة تلقائياً (15% ضريبة القيمة المضافة)
- إنشاء فواتير PDF احترافية
- ترقيم تلقائي للفواتير
- شعار HokTech مدمج
- حفظ تلقائي بأسماء منظمة
- إمكانية إضافة اسم العميل وملاحظات
- يعمل بدون إنترنت (Offline)

### English:
- Simple and easy-to-use interface
- Add multiple products/services
- Automatic tax calculation (15% VAT)
- Professional PDF invoice generation
- Automatic invoice numbering
- Integrated HokTech logo
- Organized automatic saving
- Customer name and notes support
- Works offline

## متطلبات التشغيل / Requirements

- Python 3.7 أو أحدث
- المكتبات المطلوبة في `requirements.txt`

## التثبيت / Installation

1. تأكد من تثبيت Python على جهازك
2. قم بتثبيت المكتبات المطلوبة:
```bash
pip install -r requirements.txt
```

أو:
```bash
pip install reportlab Pillow
```

## تشغيل التطبيق / Running the Application

```bash
python main.py
```

## كيفية الاستخدام / How to Use

### إضافة منتج:
1. أدخل اسم المنتج أو الخدمة
2. حدد الكمية
3. أدخل السعر للوحدة الواحدة
4. اضغط "إضافة" أو Enter

### إنشاء فاتورة:
1. أضف جميع المنتجات المطلوبة
2. أدخل اسم العميل (اختياري)
3. أضف ملاحظات إذا لزم الأمر
4. اضغط "إنشاء فاتورة"
5. ستظهر رسالة تأكيد مع إمكانية فتح الفاتورة

### العمليات الأخرى:
- **حذف عنصر**: حدد العنصر من الجدول واضغط "حذف العنصر"
- **مسح الكل**: اضغط "مسح الكل" لحذف جميع العناصر

## هيكل الملفات / File Structure

```
├── main.py                 # الملف الرئيسي للتطبيق
├── invoice_generator.py    # مولد الفواتير PDF
├── config.py              # إعدادات الشركة والتطبيق
├── requirements.txt       # المكتبات المطلوبة
├── invoices/             # مجلد حفظ الفواتير (ينشأ تلقائياً)
└── HOKTECH Logo Design with Circuit Elements.png  # شعار الشركة
```

## إعدادات الشركة / Company Settings

يمكنك تعديل إعدادات الشركة في ملف `config.py`:

- اسم الشركة
- العنوان
- رقم الهاتف
- البريد الإلكتروني
- الموقع الإلكتروني
- نسبة الضريبة
- العملة

## تخصيص التصميم / Design Customization

يمكنك تغيير ألوان التطبيق في ملف `config.py`:

```python
PRIMARY_COLOR = "#2E86AB"    # اللون الأساسي
SECONDARY_COLOR = "#A23B72"  # اللون الثانوي
ACCENT_COLOR = "#F18F01"     # لون التمييز
```

## الفواتير المحفوظة / Saved Invoices

- تحفظ الفواتير في مجلد `invoices/`
- تسمية تلقائية: `Invoice-0001.pdf`, `Invoice-0002.pdf`, إلخ
- تحتوي كل فاتورة على:
  - شعار HokTech
  - رقم الفاتورة والتاريخ
  - معلومات العميل
  - جدول المنتجات
  - المجموع الفرعي والضريبة والإجمالي
  - معلومات الشركة

## الدعم الفني / Technical Support

للمساعدة أو الاستفسارات، يرجى التواصل مع فريق HokTech.

## الترخيص / License

هذا التطبيق مطور خصيصاً لشركة HokTech.

---

**تم التطوير بواسطة Augment Agent لشركة HokTech**
