@echo off
echo ========================================
echo    HokTech POS - Fixed Print Version
echo    نسخة محسنة من الطباعة المباشرة
echo ========================================
echo.

echo Stopping any running instances...
taskkill /f /im HokTechPOS.exe 2>nul
timeout /t 2 /nobreak >nul

echo Building the application...
dotnet build HokTechPOS.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the FIXED Print system...
    echo.
    echo ✅ FIXES APPLIED:
    echo    - Removed "(Arabic)" suffix from printed text
    echo    - Added HokTech logo in printed invoice
    echo    - Improved Arabic text handling
    echo.
    echo Features:
    echo ✅ Direct printing to A4 paper
    echo ✅ Clean Arabic text (no Arabic suffix)
    echo ✅ HokTech logo in printed invoice
    echo ✅ Egyptian Pounds (EGP) currency
    echo ✅ Professional invoice layout
    echo.
    echo Test Instructions:
    echo 1. Add Arabic product: "تطبيقات"
    echo 2. Click "Print Invoice"
    echo 3. Check printed result:
    echo    - Should show "تطبيقات" (clean, no Arabic suffix)
    echo    - Should show HokTech logo at top
    echo.
    dotnet run --project HokTechPOS.csproj --configuration Release
) else (
    echo.
    echo Build failed! Please check the errors above.
    pause
)
