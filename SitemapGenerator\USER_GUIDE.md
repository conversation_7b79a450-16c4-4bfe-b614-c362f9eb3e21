# 📖 دليل المستخدم الشامل - مولد خرائط المواقع للسيو
# 📖 Comprehensive User Guide - SEO Sitemap Generator

## 🎯 مقدمة / Introduction

مرحباً بك في **مولد خرائط المواقع للسيو** من HokTech! هذا الدليل سيساعدك على استخدام جميع ميزات التطبيق بفعالية لإنشاء خرائط مواقع احترافية ومتوافقة مع معايير Google SEO.

Welcome to **SEO Sitemap Generator** by HokTech! This guide will help you use all application features effectively to create professional sitemaps compliant with Google SEO standards.

---

## 🚀 البدء السريع / Quick Start

### 1️⃣ **تشغيل التطبيق / Running the Application**

```bash
# الطريقة الأسهل - انقر مرتين
run_sitemap_generator.bat

# أو من سطر الأوامر
dotnet run --project SitemapGenerator.csproj
```

### 2️⃣ **الخطوات الأساسية / Basic Steps**

1. **أدخل رابط الموقع** في تبويب "Website Crawl"
2. **ابدأ الزحف** بالضغط على "Start Crawl"
3. **راجع الروابط** في تبويب "URLs Management"
4. **اضبط الإعدادات** في تبويب "Settings"
5. **أنشئ خريطة الموقع** في تبويب "Generate Sitemap"

---

## 🔍 تبويب الزحف / Website Crawl Tab

### 📋 **الوظائف الأساسية / Basic Functions**

#### 🌐 **إدخال رابط الموقع / Website URL Input**
```
✅ صحيح: https://example.com
✅ صحيح: https://www.mysite.com
❌ خطأ: example.com (بدون https://)
❌ خطأ: www.site (غير مكتمل)
```

#### ▶️ **بدء الزحف / Start Crawling**
- اضغط **"Start Crawl"** لبدء عملية الزحف
- راقب شريط التقدم والحالة
- اقرأ رسائل السجل للتفاصيل

#### ⏹️ **إيقاف الزحف / Stop Crawling**
- اضغط **"Stop Crawl"** لإيقاف العملية
- سيتم حفظ الروابط المكتشفة حتى نقطة الإيقاف

### 📊 **مراقبة التقدم / Progress Monitoring**

#### 📈 **شريط التقدم / Progress Bar**
- يعرض حالة الزحف الحالية
- يتحرك أثناء معالجة الصفحات

#### 📋 **معلومات الحالة / Status Information**
```
Processed: 25    # عدد الصفحات المعالجة
Discovered: 150  # عدد الروابط المكتشفة
```

#### 📝 **سجل الزحف / Crawl Log**
```
[14:30:15] Starting website crawl...
[14:30:16] Found: https://example.com/about
[14:30:17] Found: https://example.com/services
[14:30:18] Crawl completed! Discovered 45 URLs.
```

---

## 📋 تبويب إدارة الروابط / URLs Management Tab

### 🔍 **البحث والتصفية / Search & Filter**

#### 🔎 **مربع البحث / Search Box**
```
البحث بالرابط: "about"
البحث بالعنوان: "contact"
البحث المختلط: "services"
```

### 📊 **جدول الروابط / URLs Table**

#### 📋 **الأعمدة المتاحة / Available Columns**
- **Include** ✅ - تضمين في خريطة الموقع
- **URL** 🔗 - الرابط الكامل
- **Title** 📄 - عنوان الصفحة
- **Change Frequency** 🔄 - تكرار التحديث
- **Priority** ⭐ - الأولوية (0.0-1.0)
- **Last Modified** 📅 - تاريخ آخر تعديل

### ➕ **إضافة روابط جديدة / Adding New URLs**

#### 🆕 **زر "Add URL"**
1. اضغط **"Add URL"**
2. املأ النموذج:
   ```
   URL: https://example.com/new-page
   Title: صفحة جديدة
   Description: وصف الصفحة
   Keywords: كلمة1, كلمة2, كلمة3
   Language: ar (للعربية) أو en (للإنجليزية)
   Change Frequency: Weekly
   Priority: 0.8
   ```
3. اضغط **"OK"** للحفظ

### ✏️ **تعديل الروابط / Editing URLs**

#### 🔧 **زر "Edit"**
1. حدد الرابط من الجدول
2. اضغط **"Edit"**
3. عدّل المعلومات المطلوبة
4. اضغط **"OK"** للحفظ

### 🗑️ **حذف الروابط / Deleting URLs**

#### ❌ **زر "Delete"**
- حدد الرابط المراد حذفه
- اضغط **"Delete"**
- أكد الحذف

#### 🧹 **زر "Clear All"**
- يحذف جميع الروابط
- يطلب تأكيد قبل الحذف

---

## ⚙️ تبويب الإعدادات / Settings Tab

### 🌐 **الإعدادات الأساسية / Basic Settings**

#### 🏠 **معلومات الموقع / Website Information**
```json
{
  "BaseUrl": "https://example.com",
  "WebsiteName": "موقعي الرائع",
  "DefaultLanguage": "ar",
  "SupportedLanguages": ["ar", "en"]
}
```

#### 📁 **إعدادات الإخراج / Output Settings**
```json
{
  "OutputDirectory": "sitemaps",
  "MaxUrlsPerSitemap": 50000,
  "MaxSitemapSizeMB": 50,
  "CompressSitemaps": true
}
```

### 🔍 **إعدادات الزحف / Crawl Settings**

#### 🎯 **معايير الزحف / Crawl Parameters**
```json
{
  "MaxDepth": 5,                    // أقصى عمق
  "MaxPages": 10000,                // أقصى عدد صفحات
  "DelayBetweenRequests": 1000,     // تأخير (مللي ثانية)
  "TimeoutSeconds": 30,             // مهلة الطلب
  "UserAgent": "SEO Sitemap Generator Bot 1.0"
}
```

#### 🚫 **استبعاد المحتوى / Content Exclusion**
```json
{
  "ExcludePatterns": [
    "/admin/",      // صفحات الإدارة
    "/wp-admin/",   // إدارة WordPress
    "/login",       // صفحات تسجيل الدخول
    "/search",      // صفحات البحث
    "?print=",      // نسخ الطباعة
    "#"             // الروابط المحلية
  ]
}
```

### 🎨 **أنواع خرائط المواقع / Sitemap Types**

#### ✅ **تفعيل الأنواع / Enable Types**
- **Include Image Sitemap** 🖼️ - خريطة الصور
- **Include Video Sitemap** 🎥 - خريطة الفيديو
- **Include News Sitemap** 📰 - خريطة الأخبار
- **Generate Robots.txt** 🤖 - ملف robots.txt

### 🔧 **إعدادات SEO / SEO Settings**

#### 🎯 **تحسين محركات البحث / Search Engine Optimization**
```json
{
  "ValidateUrls": true,           // التحقق من الروابط
  "CheckDuplicateContent": true,  // فحص المحتوى المكرر
  "AnalyzeSeoElements": true,     // تحليل عناصر SEO
  "DetectCanonicalUrls": true,    // اكتشاف الروابط الأساسية
  "IncludeHreflang": true,        // تضمين hreflang
  "MinimumPriority": 0.1          // أقل أولوية مقبولة
}
```

---

## 🎯 تبويب إنشاء خريطة الموقع / Generate Sitemap Tab

### 🚀 **إنشاء خريطة الموقع / Generate Sitemap**

#### 📋 **قبل الإنشاء / Before Generation**
تأكد من:
- ✅ وجود روابط في قائمة URLs
- ✅ ضبط الإعدادات المناسبة
- ✅ تحديد مجلد الإخراج

#### ▶️ **بدء الإنشاء / Start Generation**
1. اضغط **"Generate Sitemap Files"**
2. راقب شريط التقدم
3. اقرأ رسائل السجل

### 📁 **الملفات المُنشأة / Generated Files**

#### 📄 **الملفات الأساسية / Basic Files**
```
✅ sitemap.xml              # خريطة الموقع الرئيسية
✅ sitemap-index.xml        # فهرس خرائط المواقع (للمواقع الكبيرة)
✅ robots.txt               # توجيهات محركات البحث
```

#### 🎨 **الملفات المتخصصة / Specialized Files**
```
🖼️ sitemap-images.xml       # خريطة الصور
🎥 sitemap-videos.xml       # خريطة الفيديو
📰 sitemap-news.xml         # خريطة الأخبار
```

#### 📦 **الملفات المضغوطة / Compressed Files**
```
🗜️ sitemap.xml.gz          # نسخة مضغوطة (اختيارية)
🗜️ sitemap-images.xml.gz   # صور مضغوطة
```

### 📊 **تقرير النتائج / Results Report**

#### ✅ **نجح الإنشاء / Successful Generation**
```
✅ Successfully generated 3 sitemap files with 245 URLs.

Generated files:
  • sitemap.xml
  • sitemap-images.xml
  • robots.txt
```

#### ❌ **فشل الإنشاء / Failed Generation**
```
❌ Error generating sitemap: Invalid URL format detected

Check the URLs tab for invalid entries.
```

---

## 🛠️ استكشاف الأخطاء / Troubleshooting

### ❌ **مشاكل الزحف / Crawling Issues**

#### 🔗 **خطأ الاتصال / Connection Error**
```
المشكلة: Cannot connect to website
الحل:
1. تحقق من صحة الرابط
2. تأكد من اتصال الإنترنت
3. جرب رابط آخر للاختبار
```

#### 🚫 **رفض الوصول / Access Denied**
```
المشكلة: HTTP 403 Forbidden
الحل:
1. تحقق من ملف robots.txt
2. قلل سرعة الزحف (زيادة DelayBetweenRequests)
3. غيّر User Agent في الإعدادات
```

#### ⏱️ **انتهاء المهلة / Timeout**
```
المشكلة: Request timeout
الحل:
1. زيادة TimeoutSeconds في الإعدادات
2. تحقق من سرعة الإنترنت
3. جرب في وقت آخر
```

### 💾 **مشاكل الحفظ / Saving Issues**

#### 📁 **خطأ في المجلد / Directory Error**
```
المشكلة: Cannot create output directory
الحل:
1. تحقق من صلاحيات الكتابة
2. غيّر مجلد الإخراج
3. شغّل التطبيق كمدير
```

#### 💿 **مساحة القرص / Disk Space**
```
المشكلة: Insufficient disk space
الحل:
1. احذف ملفات غير ضرورية
2. غيّر مجلد الإخراج لقرص آخر
3. فعّل ضغط الملفات
```

---

## 🎯 أمثلة عملية / Practical Examples

### 🏢 **موقع شركة / Corporate Website**

#### 📋 **الإعدادات المقترحة / Suggested Settings**
```json
{
  "MaxDepth": 3,
  "MaxPages": 1000,
  "DelayBetweenRequests": 500,
  "IncludeImageSitemap": true,
  "DefaultChangeFrequency": "Monthly",
  "DefaultPriority": 0.6
}
```

#### 🎯 **أولويات الصفحات / Page Priorities**
```
الصفحة الرئيسية: 1.0
من نحن: 0.8
الخدمات: 0.9
اتصل بنا: 0.7
المدونة: 0.6
صفحات المنتجات: 0.8
```

### 📰 **موقع إخباري / News Website**

#### 📋 **الإعدادات المقترحة / Suggested Settings**
```json
{
  "MaxDepth": 4,
  "MaxPages": 5000,
  "DelayBetweenRequests": 200,
  "IncludeNewsSitemap": true,
  "DefaultChangeFrequency": "Daily",
  "DefaultPriority": 0.7
}
```

#### 🎯 **تكرار التحديث / Update Frequency**
```
الأخبار العاجلة: Always
الأخبار اليومية: Daily
المقالات: Weekly
الأرشيف: Monthly
```

### 🛒 **متجر إلكتروني / E-commerce Store**

#### 📋 **الإعدادات المقترحة / Suggested Settings**
```json
{
  "MaxDepth": 5,
  "MaxPages": 10000,
  "DelayBetweenRequests": 300,
  "IncludeImageSitemap": true,
  "DefaultChangeFrequency": "Weekly",
  "DefaultPriority": 0.7
}
```

#### 🎯 **أولويات المنتجات / Product Priorities**
```
الصفحة الرئيسية: 1.0
فئات المنتجات: 0.9
صفحات المنتجات: 0.8
عروض خاصة: 0.9
سياسات المتجر: 0.5
```

---

## 🔧 نصائح متقدمة / Advanced Tips

### ⚡ **تحسين الأداء / Performance Optimization**

#### 🚀 **زيادة سرعة الزحف / Increase Crawl Speed**
```json
{
  "DelayBetweenRequests": 100,    // تقليل التأخير
  "TimeoutSeconds": 15,           // تقليل المهلة
  "MaxDepth": 3                   // تقليل العمق
}
```

#### 🛡️ **تجنب الحظر / Avoid Blocking**
```json
{
  "DelayBetweenRequests": 2000,   // زيادة التأخير
  "RespectRobotsTxt": true,       // احترام robots.txt
  "UserAgent": "Friendly Bot"     // وكيل مستخدم ودود
}
```

### 🎯 **تحسين SEO / SEO Optimization**

#### 📈 **أولويات ذكية / Smart Priorities**
```
الصفحات القريبة من الجذر: أولوية عالية
الصفحات العميقة: أولوية منخفضة
الصفحات المهمة: أولوية مخصصة
```

#### 🌐 **دعم متعدد اللغات / Multi-language Support**
```json
{
  "SupportedLanguages": ["ar", "en", "fr"],
  "IncludeHreflang": true,
  "DefaultLanguage": "ar"
}
```

---

## 📞 الدعم والمساعدة / Support & Help

### 🆘 **الحصول على المساعدة / Getting Help**

#### 📖 **المصادر المتاحة / Available Resources**
- **هذا الدليل** - معلومات شاملة
- **رسائل الخطأ** - تفاصيل المشاكل
- **سجل التطبيق** - تتبع العمليات

#### 🔍 **تشخيص المشاكل / Problem Diagnosis**
1. **اقرأ رسالة الخطأ** بعناية
2. **تحقق من الإعدادات** المستخدمة
3. **جرب إعدادات مختلفة** للاختبار
4. **راجع سجل العمليات** للتفاصيل

### 💡 **نصائح عامة / General Tips**

#### ✅ **أفضل الممارسات / Best Practices**
- ابدأ بإعدادات محافظة
- اختبر على موقع صغير أولاً
- احفظ نسخة احتياطية من الإعدادات
- راجع النتائج قبل النشر

#### 🔄 **صيانة دورية / Regular Maintenance**
- حدّث خرائط المواقع شهرياً
- راجع الروابط المكسورة
- تحقق من تحديثات التطبيق
- نظّف الملفات القديمة

---

## 🏆 **استمتع بمولد خرائط المواقع الاحترافي!**

هذا الدليل يغطي جميع جوانب استخدام **مولد خرائط المواقع للسيو** من HokTech. مع هذه المعلومات، ستتمكن من إنشاء خرائط مواقع احترافية ومتوافقة مع معايير Google SEO بسهولة وفعالية.

**🎯 نجاحك في SEO يبدأ من هنا!**

---

*تم إعداد هذا الدليل بواسطة Augment Agent لشركة HokTech*  
*💎 دليل شامل - لاستخدام مثالي!*
