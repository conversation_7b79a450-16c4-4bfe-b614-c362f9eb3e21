import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import subprocess
import platform
from invoice_generator_english import EnglishInvoiceGenerator
import config

class POSApp:
    def __init__(self, root):
        self.root = root
        self.root.title("HokTech POS System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # إنشاء مولد الفواتير
        self.invoice_generator = EnglishInvoiceGenerator()
        
        # قائمة المنتجات
        self.items = []
        
        # إعداد واجهة المستخدم
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg=config.PRIMARY_COLOR, height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame,
            text="HokTech POS System\nPoint of Sale",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg=config.PRIMARY_COLOR
        )
        title_label.pack(expand=True)
        
        # Product input frame
        input_frame = tk.LabelFrame(self.root, text="Add Product", font=('Arial', 12, 'bold'))
        input_frame.pack(fill='x', padx=10, pady=10)

        # Product name
        tk.Label(input_frame, text="Product Name:", font=('Arial', 10)).grid(row=0, column=0, sticky='w', padx=5, pady=5)
        self.product_name = tk.Entry(input_frame, font=('Arial', 10), width=30)
        self.product_name.grid(row=0, column=1, padx=5, pady=5)

        # Quantity
        tk.Label(input_frame, text="Quantity:", font=('Arial', 10)).grid(row=0, column=2, sticky='w', padx=5, pady=5)
        self.quantity = tk.Spinbox(input_frame, from_=1, to=1000, font=('Arial', 10), width=10)
        self.quantity.grid(row=0, column=3, padx=5, pady=5)

        # Price
        tk.Label(input_frame, text="Price (SAR):", font=('Arial', 10)).grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.price = tk.Entry(input_frame, font=('Arial', 10), width=15)
        self.price.grid(row=1, column=1, padx=5, pady=5)

        # Add button
        add_button = tk.Button(
            input_frame,
            text="Add Product",
            command=self.add_item,
            bg=config.ACCENT_COLOR,
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15
        )
        add_button.grid(row=1, column=2, columnspan=2, padx=5, pady=10)
        
        # Products table
        table_frame = tk.LabelFrame(self.root, text="Products List", font=('Arial', 12, 'bold'))
        table_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Create table
        columns = ('Product', 'Quantity', 'Price', 'Total')
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=10)

        # Define columns
        self.tree.heading('Product', text='Product')
        self.tree.heading('Quantity', text='Quantity')
        self.tree.heading('Price', text='Price (SAR)')
        self.tree.heading('Total', text='Total (SAR)')
        
        # تحديد عرض الأعمدة
        self.tree.column('Product', width=250)
        self.tree.column('Quantity', width=100)
        self.tree.column('Price', width=120)
        self.tree.column('Total', width=120)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Customer info and buttons frame
        bottom_frame = tk.Frame(self.root)
        bottom_frame.pack(fill='x', padx=10, pady=10)

        # Customer information
        customer_frame = tk.LabelFrame(bottom_frame, text="Customer Information", font=('Arial', 10, 'bold'))
        customer_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))

        tk.Label(customer_frame, text="Customer Name:", font=('Arial', 9)).pack(anchor='w', padx=5, pady=2)
        self.customer_name = tk.Entry(customer_frame, font=('Arial', 9))
        self.customer_name.pack(fill='x', padx=5, pady=2)

        tk.Label(customer_frame, text="Notes:", font=('Arial', 9)).pack(anchor='w', padx=5, pady=2)
        self.notes = tk.Text(customer_frame, height=3, font=('Arial', 9))
        self.notes.pack(fill='x', padx=5, pady=2)
        
        # إطار الأزرار والمجموع
        actions_frame = tk.Frame(bottom_frame)
        actions_frame.pack(side='right')
        
        # Display total
        self.total_label = tk.Label(
            actions_frame,
            text="Total: 0.00 SAR",
            font=('Arial', 14, 'bold'),
            fg=config.PRIMARY_COLOR
        )
        self.total_label.pack(pady=5)

        # Action buttons
        buttons_frame = tk.Frame(actions_frame)
        buttons_frame.pack()

        # Delete item button
        delete_button = tk.Button(
            buttons_frame,
            text="Delete\nItem",
            command=self.delete_item,
            bg='#dc3545',
            fg='white',
            font=('Arial', 9, 'bold'),
            width=12,
            height=2
        )
        delete_button.pack(side='left', padx=2)

        # Clear all button
        clear_button = tk.Button(
            buttons_frame,
            text="Clear\nAll",
            command=self.clear_all,
            bg='#6c757d',
            fg='white',
            font=('Arial', 9, 'bold'),
            width=12,
            height=2
        )
        clear_button.pack(side='left', padx=2)

        # Generate invoice button
        invoice_button = tk.Button(
            buttons_frame,
            text="Generate\nInvoice",
            command=self.generate_invoice,
            bg=config.PRIMARY_COLOR,
            fg='white',
            font=('Arial', 10, 'bold'),
            width=15,
            height=2
        )
        invoice_button.pack(side='left', padx=2)
        
        # ربط الأحداث
        self.product_name.bind('<Return>', lambda e: self.quantity.focus())
        self.quantity.bind('<Return>', lambda e: self.price.focus())
        self.price.bind('<Return>', lambda e: self.add_item())
        
    def add_item(self):
        """إضافة منتج جديد"""
        name = self.product_name.get().strip()
        try:
            qty = int(self.quantity.get())
            price = float(self.price.get())
        except ValueError:
            messagebox.showerror("Error", "Please enter valid quantity and price")
            return

        if not name:
            messagebox.showerror("Error", "Please enter product name")
            return

        if qty <= 0 or price <= 0:
            messagebox.showerror("Error", "Quantity and price must be greater than zero")
            return
        
        # إضافة المنتج للقائمة
        total = qty * price
        item = {
            'name': name,
            'quantity': qty,
            'price': price
        }
        self.items.append(item)
        
        # Add to table
        self.tree.insert('', 'end', values=(name, qty, f"{price:.2f}", f"{total:.2f}"))

        # Clear fields
        self.product_name.delete(0, 'end')
        self.quantity.delete(0, 'end')
        self.quantity.insert(0, '1')
        self.price.delete(0, 'end')

        # Update total
        self.update_total()

        # Focus on product name field
        self.product_name.focus()
    
    def delete_item(self):
        """Delete selected item"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select an item to delete")
            return

        # Get item index
        item_index = self.tree.index(selected[0])

        # Delete from list and table
        del self.items[item_index]
        self.tree.delete(selected[0])

        # Update total
        self.update_total()

    def clear_all(self):
        """Clear all items"""
        if self.items and messagebox.askyesno("Confirm", "Do you want to clear all items?"):
            self.items.clear()
            for item in self.tree.get_children():
                self.tree.delete(item)
            self.update_total()

    def update_total(self):
        """Update total amount"""
        total = sum(item['quantity'] * item['price'] for item in self.items)
        self.total_label.config(text=f"Total: {total:.2f} SAR")
    
    def generate_invoice(self):
        """Generate invoice"""
        if not self.items:
            messagebox.showwarning("Warning", "Please add products first")
            return

        try:
            # Generate invoice
            customer = self.customer_name.get().strip()
            notes = self.notes.get(1.0, 'end').strip()

            filepath = self.invoice_generator.generate_invoice(self.items, customer, notes)

            # Show success message
            messagebox.showinfo(
                "Success",
                f"Invoice created successfully!\n\nFile: {os.path.basename(filepath)}"
            )

            # Open invoice
            if messagebox.askyesno("Open Invoice", "Do you want to open the invoice?"):
                self.open_file(filepath)

            # Clear data after creating invoice
            if messagebox.askyesno("Clear Data", "Do you want to clear data for a new invoice?"):
                self.clear_all()
                self.customer_name.delete(0, 'end')
                self.notes.delete(1.0, 'end')

        except Exception as e:
            messagebox.showerror("Error", f"Error creating invoice:\n{str(e)}")
    
    def open_file(self, filepath):
        """Open file with default program"""
        try:
            if platform.system() == 'Windows':
                os.startfile(filepath)
            elif platform.system() == 'Darwin':  # macOS
                subprocess.run(['open', filepath])
            else:  # Linux
                subprocess.run(['xdg-open', filepath])
        except Exception as e:
            messagebox.showerror("Error", f"Cannot open file:\n{str(e)}")

def main():
    root = tk.Tk()
    app = POSApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
