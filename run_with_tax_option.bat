@echo off
echo ========================================
echo    HokTech POS - Tax Option Version
echo    نسخة مع خيار إزالة الضرائب
echo ========================================
echo.

echo Stopping any running instances...
taskkill /f /im HokTechPOS.exe 2>nul
timeout /t 2 /nobreak >nul

echo Building the application...
dotnet build HokTechPOS.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the TAX OPTION version...
    echo.
    echo ✅ NEW FEATURE ADDED:
    echo    - Tax Option Checkbox: Include VAT (15%)
    echo    - تضمين ضريبة القيمة المضافة (خيار)
    echo    - Can enable/disable tax calculation
    echo.
    echo Features:
    echo ✅ Direct printing to A4 paper
    echo ✅ Clean Arabic text (no Arabic suffix)
    echo ✅ HokTech logo in printed invoice
    echo ✅ Tax option checkbox (NEW!)
    echo ✅ Egyptian Pounds (EGP) currency
    echo ✅ Professional invoice layout
    echo.
    echo Tax Options:
    echo ☑️ Checked = Include 15%% VAT in invoice
    echo ☐ Unchecked = No tax, subtotal = total
    echo.
    echo Test Instructions:
    echo 1. Add product: "تطبيقات" (500 EGP)
    echo 2. Check tax option:
    echo    - With tax: Total = 575 EGP (500 + 75 VAT)
    echo    - Without tax: Total = 500 EGP (no VAT line)
    echo 3. Print and verify tax calculation
    echo.
    dotnet run --project HokTechPOS.csproj --configuration Release
) else (
    echo.
    echo Build failed! Please check the errors above.
    pause
)
