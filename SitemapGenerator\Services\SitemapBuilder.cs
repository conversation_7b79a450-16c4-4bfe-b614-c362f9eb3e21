using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using SitemapGenerator.Models;

namespace SitemapGenerator.Services
{
    /// <summary>
    /// Service for building XML sitemaps according to Google standards
    /// </summary>
    public class SitemapBuilder
    {
        private readonly SitemapSettings _settings;

        public SitemapBuilder(SitemapSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
        }

        /// <summary>
        /// Generate all sitemap files
        /// </summary>
        public async Task<SitemapGenerationResult> GenerateSitemapsAsync(List<SitemapUrl> urls)
        {
            var result = new SitemapGenerationResult();
            
            try
            {
                // Ensure output directory exists
                Directory.CreateDirectory(_settings.OutputDirectory);

                // Filter and group URLs
                var validUrls = urls.Where(u => u.IsIncluded && u.Priority >= _settings.SeoSettings.MinimumPriority).ToList();
                
                // Generate main sitemap
                var mainSitemapFiles = await GenerateMainSitemapAsync(validUrls);
                result.GeneratedFiles.AddRange(mainSitemapFiles);

                // Generate specialized sitemaps
                if (_settings.IncludeImageSitemap)
                {
                    var imageSitemapFiles = await GenerateImageSitemapAsync(validUrls);
                    result.GeneratedFiles.AddRange(imageSitemapFiles);
                }

                if (_settings.IncludeVideoSitemap)
                {
                    var videoSitemapFiles = await GenerateVideoSitemapAsync(validUrls);
                    result.GeneratedFiles.AddRange(videoSitemapFiles);
                }

                if (_settings.IncludeNewsSitemap)
                {
                    var newsSitemapFiles = await GenerateNewsSitemapAsync(validUrls);
                    result.GeneratedFiles.AddRange(newsSitemapFiles);
                }

                // Generate sitemap index if multiple files
                if (result.GeneratedFiles.Count > 1)
                {
                    var indexFile = await GenerateSitemapIndexAsync(result.GeneratedFiles);
                    result.IndexFile = indexFile;
                }

                // Generate robots.txt
                if (_settings.GenerateRobotsTxt)
                {
                    var robotsFile = GenerateRobotsTxt(result.IndexFile ?? result.GeneratedFiles.FirstOrDefault());
                    result.RobotsFile = robotsFile;
                }

                result.Success = true;
                result.TotalUrls = validUrls.Count;
                result.Message = $"Successfully generated {result.GeneratedFiles.Count} sitemap files with {result.TotalUrls} URLs.";
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Message = $"Error generating sitemaps: {ex.Message}";
                result.Exception = ex;
            }

            return result;
        }

        private async Task<List<string>> GenerateMainSitemapAsync(List<SitemapUrl> urls)
        {
            var files = new List<string>();
            var pageUrls = urls.Where(u => u.UrlType == UrlType.Page).ToList();
            
            // Split into chunks if necessary
            var chunks = SplitIntoChunks(pageUrls, _settings.MaxUrlsPerSitemap);
            
            for (int i = 0; i < chunks.Count; i++)
            {
                var fileName = chunks.Count == 1 ? "sitemap.xml" : $"sitemap-{i + 1}.xml";
                var filePath = Path.Combine(_settings.OutputDirectory, fileName);
                
                await GenerateXmlSitemapAsync(chunks[i], filePath, SitemapType.Standard);
                files.Add(fileName);
            }

            return files;
        }

        private async Task<List<string>> GenerateImageSitemapAsync(List<SitemapUrl> urls)
        {
            var files = new List<string>();
            var urlsWithImages = urls.Where(u => u.Images.Any()).ToList();
            
            if (!urlsWithImages.Any()) return files;

            var chunks = SplitIntoChunks(urlsWithImages, _settings.MaxUrlsPerSitemap);
            
            for (int i = 0; i < chunks.Count; i++)
            {
                var fileName = chunks.Count == 1 ? "sitemap-images.xml" : $"sitemap-images-{i + 1}.xml";
                var filePath = Path.Combine(_settings.OutputDirectory, fileName);
                
                await GenerateXmlSitemapAsync(chunks[i], filePath, SitemapType.Image);
                files.Add(fileName);
            }

            return files;
        }

        private async Task<List<string>> GenerateVideoSitemapAsync(List<SitemapUrl> urls)
        {
            var files = new List<string>();
            var urlsWithVideos = urls.Where(u => u.Video != null).ToList();
            
            if (!urlsWithVideos.Any()) return files;

            var chunks = SplitIntoChunks(urlsWithVideos, _settings.MaxUrlsPerSitemap);
            
            for (int i = 0; i < chunks.Count; i++)
            {
                var fileName = chunks.Count == 1 ? "sitemap-videos.xml" : $"sitemap-videos-{i + 1}.xml";
                var filePath = Path.Combine(_settings.OutputDirectory, fileName);
                
                await GenerateXmlSitemapAsync(chunks[i], filePath, SitemapType.Video);
                files.Add(fileName);
            }

            return files;
        }

        private async Task<List<string>> GenerateNewsSitemapAsync(List<SitemapUrl> urls)
        {
            var files = new List<string>();
            var newsUrls = urls.Where(u => u.News != null && 
                                          u.News.PublicationDate >= DateTime.Now.AddDays(-2)).ToList(); // News sitemap only includes last 2 days
            
            if (!newsUrls.Any()) return files;

            var fileName = "sitemap-news.xml";
            var filePath = Path.Combine(_settings.OutputDirectory, fileName);
            
            await GenerateXmlSitemapAsync(newsUrls, filePath, SitemapType.News);
            files.Add(fileName);

            return files;
        }

        private async Task GenerateXmlSitemapAsync(List<SitemapUrl> urls, string filePath, SitemapType type)
        {
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                Async = true
            };

            using var writer = XmlWriter.Create(filePath, settings);
            
            await writer.WriteStartDocumentAsync();
            
            // Root element with namespaces
            await writer.WriteStartElementAsync(null, "urlset", "http://www.sitemaps.org/schemas/sitemap/0.9");
            
            if (type == SitemapType.Image)
            {
                await writer.WriteAttributeStringAsync("xmlns", "image", null, "http://www.google.com/schemas/sitemap-image/1.1");
            }
            else if (type == SitemapType.Video)
            {
                await writer.WriteAttributeStringAsync("xmlns", "video", null, "http://www.google.com/schemas/sitemap-video/1.1");
            }
            else if (type == SitemapType.News)
            {
                await writer.WriteAttributeStringAsync("xmlns", "news", null, "http://www.google.com/schemas/sitemap-news/0.9");
            }

            if (_settings.SeoSettings.IncludeHreflang && _settings.SupportedLanguages.Count > 1)
            {
                await writer.WriteAttributeStringAsync("xmlns", "xhtml", null, "http://www.w3.org/1999/xhtml");
            }

            foreach (var url in urls)
            {
                await WriteUrlElementAsync(writer, url, type);
            }

            await writer.WriteEndElementAsync(); // urlset
            await writer.WriteEndDocumentAsync();
            
            // Compress if enabled
            if (_settings.CompressSitemaps)
            {
                await CompressFileAsync(filePath);
            }
        }

        private async Task WriteUrlElementAsync(XmlWriter writer, SitemapUrl url, SitemapType type)
        {
            await writer.WriteStartElementAsync(null, "url", null);
            
            // Basic URL information
            await writer.WriteElementStringAsync(null, "loc", null, url.Url);
            await writer.WriteElementStringAsync(null, "lastmod", null, url.LastModified.ToString("yyyy-MM-ddTHH:mm:sszzz"));
            await writer.WriteElementStringAsync(null, "changefreq", null, url.ChangeFrequency.ToString().ToLowerInvariant());
            await writer.WriteElementStringAsync(null, "priority", null, url.Priority.ToString("F1"));

            // Hreflang for multi-language sites
            if (_settings.SeoSettings.IncludeHreflang && _settings.SupportedLanguages.Count > 1)
            {
                foreach (var lang in _settings.SupportedLanguages)
                {
                    var hrefUrl = GenerateHreflangUrl(url.Url, lang);
                    await writer.WriteStartElementAsync("xhtml", "link", "http://www.w3.org/1999/xhtml");
                    await writer.WriteAttributeStringAsync(null, "rel", null, "alternate");
                    await writer.WriteAttributeStringAsync(null, "hreflang", null, lang);
                    await writer.WriteAttributeStringAsync(null, "href", null, hrefUrl);
                    await writer.WriteEndElementAsync();
                }
            }

            // Type-specific elements
            switch (type)
            {
                case SitemapType.Image:
                    await WriteImageElementsAsync(writer, url);
                    break;
                case SitemapType.Video:
                    await WriteVideoElementsAsync(writer, url);
                    break;
                case SitemapType.News:
                    await WriteNewsElementsAsync(writer, url);
                    break;
            }

            await writer.WriteEndElementAsync(); // url
        }

        private async Task WriteImageElementsAsync(XmlWriter writer, SitemapUrl url)
        {
            foreach (var image in url.Images)
            {
                await writer.WriteStartElementAsync("image", "image", "http://www.google.com/schemas/sitemap-image/1.1");
                await writer.WriteElementStringAsync("image", "loc", "http://www.google.com/schemas/sitemap-image/1.1", image.Url);
                
                if (!string.IsNullOrEmpty(image.Caption))
                    await writer.WriteElementStringAsync("image", "caption", "http://www.google.com/schemas/sitemap-image/1.1", image.Caption);
                
                if (!string.IsNullOrEmpty(image.Title))
                    await writer.WriteElementStringAsync("image", "title", "http://www.google.com/schemas/sitemap-image/1.1", image.Title);
                
                if (!string.IsNullOrEmpty(image.License))
                    await writer.WriteElementStringAsync("image", "license", "http://www.google.com/schemas/sitemap-image/1.1", image.License);
                
                await writer.WriteEndElementAsync();
            }
        }

        private async Task WriteVideoElementsAsync(XmlWriter writer, SitemapUrl url)
        {
            if (url.Video == null) return;

            await writer.WriteStartElementAsync("video", "video", "http://www.google.com/schemas/sitemap-video/1.1");
            await writer.WriteElementStringAsync("video", "thumbnail_loc", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.ThumbnailUrl);
            await writer.WriteElementStringAsync("video", "title", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.Title);
            await writer.WriteElementStringAsync("video", "description", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.Description);
            
            if (!string.IsNullOrEmpty(url.Video.ContentUrl))
                await writer.WriteElementStringAsync("video", "content_loc", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.ContentUrl);
            
            if (!string.IsNullOrEmpty(url.Video.PlayerUrl))
                await writer.WriteElementStringAsync("video", "player_loc", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.PlayerUrl);
            
            if (url.Video.Duration > 0)
                await writer.WriteElementStringAsync("video", "duration", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.Duration.ToString());
            
            await writer.WriteElementStringAsync("video", "publication_date", "http://www.google.com/schemas/sitemap-video/1.1", 
                url.Video.PublicationDate.ToString("yyyy-MM-ddTHH:mm:sszzz"));
            
            if (!string.IsNullOrEmpty(url.Video.Category))
                await writer.WriteElementStringAsync("video", "category", "http://www.google.com/schemas/sitemap-video/1.1", url.Video.Category);
            
            foreach (var tag in url.Video.Tags)
            {
                await writer.WriteElementStringAsync("video", "tag", "http://www.google.com/schemas/sitemap-video/1.1", tag);
            }
            
            await writer.WriteEndElementAsync();
        }

        private async Task WriteNewsElementsAsync(XmlWriter writer, SitemapUrl url)
        {
            if (url.News == null) return;

            await writer.WriteStartElementAsync("news", "news", "http://www.google.com/schemas/sitemap-news/0.9");
            
            await writer.WriteStartElementAsync("news", "publication", "http://www.google.com/schemas/sitemap-news/0.9");
            await writer.WriteElementStringAsync("news", "name", "http://www.google.com/schemas/sitemap-news/0.9", url.News.PublicationName);
            await writer.WriteElementStringAsync("news", "language", "http://www.google.com/schemas/sitemap-news/0.9", url.News.Language);
            await writer.WriteEndElementAsync();
            
            await writer.WriteElementStringAsync("news", "publication_date", "http://www.google.com/schemas/sitemap-news/0.9", 
                url.News.PublicationDate.ToString("yyyy-MM-ddTHH:mm:sszzz"));
            await writer.WriteElementStringAsync("news", "title", "http://www.google.com/schemas/sitemap-news/0.9", url.News.Title);
            
            if (!string.IsNullOrEmpty(url.News.Keywords))
                await writer.WriteElementStringAsync("news", "keywords", "http://www.google.com/schemas/sitemap-news/0.9", url.News.Keywords);
            
            foreach (var genre in url.News.Genres)
            {
                await writer.WriteElementStringAsync("news", "genres", "http://www.google.com/schemas/sitemap-news/0.9", genre);
            }
            
            await writer.WriteEndElementAsync();
        }

        private string GenerateHreflangUrl(string baseUrl, string language)
        {
            // Simple implementation - you might want to customize this based on your URL structure
            var uri = new Uri(baseUrl);
            if (language == _settings.DefaultLanguage)
                return baseUrl;
            
            return $"{uri.Scheme}://{uri.Host}/{language}{uri.PathAndQuery}";
        }

        private async Task<string> GenerateSitemapIndexAsync(List<string> sitemapFiles)
        {
            var indexFileName = "sitemap-index.xml";
            var indexFilePath = Path.Combine(_settings.OutputDirectory, indexFileName);
            
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                Encoding = Encoding.UTF8,
                Async = true
            };

            using var writer = XmlWriter.Create(indexFilePath, settings);
            
            await writer.WriteStartDocumentAsync();
            await writer.WriteStartElementAsync(null, "sitemapindex", "http://www.sitemaps.org/schemas/sitemap/0.9");

            foreach (var sitemapFile in sitemapFiles)
            {
                await writer.WriteStartElementAsync(null, "sitemap", null);
                
                var sitemapUrl = $"{_settings.BaseUrl.TrimEnd('/')}/{sitemapFile}";
                await writer.WriteElementStringAsync(null, "loc", null, sitemapUrl);
                
                var filePath = Path.Combine(_settings.OutputDirectory, sitemapFile);
                if (File.Exists(filePath))
                {
                    var lastMod = File.GetLastWriteTime(filePath);
                    await writer.WriteElementStringAsync(null, "lastmod", null, lastMod.ToString("yyyy-MM-ddTHH:mm:sszzz"));
                }
                
                await writer.WriteEndElementAsync(); // sitemap
            }

            await writer.WriteEndElementAsync(); // sitemapindex
            await writer.WriteEndDocumentAsync();

            if (_settings.CompressSitemaps)
            {
                await CompressFileAsync(indexFilePath);
            }

            return indexFileName;
        }

        private string GenerateRobotsTxt(string? mainSitemapFile)
        {
            var robotsFileName = "robots.txt";
            var robotsFilePath = Path.Combine(_settings.OutputDirectory, robotsFileName);
            
            var content = new StringBuilder();
            content.AppendLine("User-agent: *");
            content.AppendLine("Allow: /");
            content.AppendLine();
            
            if (!string.IsNullOrEmpty(mainSitemapFile))
            {
                var sitemapUrl = $"{_settings.BaseUrl.TrimEnd('/')}/{mainSitemapFile}";
                content.AppendLine($"Sitemap: {sitemapUrl}");
            }
            
            File.WriteAllText(robotsFilePath, content.ToString());
            return robotsFileName;
        }

        private async Task CompressFileAsync(string filePath)
        {
            var compressedPath = filePath + ".gz";
            
            using var originalFileStream = File.OpenRead(filePath);
            using var compressedFileStream = File.Create(compressedPath);
            using var compressionStream = new GZipStream(compressedFileStream, CompressionMode.Compress);
            
            await originalFileStream.CopyToAsync(compressionStream);
        }

        private List<List<T>> SplitIntoChunks<T>(List<T> source, int chunkSize)
        {
            var chunks = new List<List<T>>();
            for (int i = 0; i < source.Count; i += chunkSize)
            {
                chunks.Add(source.Skip(i).Take(chunkSize).ToList());
            }
            return chunks.Any() ? chunks : new List<List<T>> { new List<T>() };
        }
    }

    public enum SitemapType
    {
        Standard,
        Image,
        Video,
        News
    }

    public class SitemapGenerationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<string> GeneratedFiles { get; set; } = new List<string>();
        public string? IndexFile { get; set; }
        public string? RobotsFile { get; set; }
        public int TotalUrls { get; set; }
        public Exception? Exception { get; set; }
    }
}
