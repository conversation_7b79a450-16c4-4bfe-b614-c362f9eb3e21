@echo off
echo ========================================
echo    Restarting HokTech POS - Print A4
echo    إعادة تشغيل نظام الطباعة المباشرة
echo ========================================
echo.

echo Stopping any running instances...
taskkill /f /im HokTechPOS.exe 2>nul
timeout /t 2 /nobreak >nul

echo Building the application...
dotnet build HokTechPOS.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the Direct Print A4 system...
    echo.
    echo New Features:
    echo ✅ Direct printing to A4 paper
    echo ✅ Arabic text support in print
    echo ✅ Egyptian Pounds (EGP) currency
    echo ✅ Professional invoice layout
    echo ✅ Two print options: Direct or Choose Printer
    echo.
    echo Instructions:
    echo 1. Add products (Arabic: تطبيقات, تطوير, etc.)
    echo 2. Click "Print Invoice" button
    echo 3. Choose print option:
    echo    - Yes = Direct Print (طباعة مباشرة)
    echo    - No = Choose Printer (اختيار الطابعة)
    echo    - Cancel = إلغاء
    echo.
    dotnet run --project HokTechPOS.csproj --configuration Release
) else (
    echo.
    echo Build failed! Please check the errors above.
    pause
)
