using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HokTechPOS.Models;
using HokTechPOS.Services;

namespace HokTechPOS
{
    public partial class MainFormFinal : Form
    {
        private readonly List<Product> _products = new List<Product>();
        private readonly InvoiceGenerator _invoiceGenerator = new InvoiceGenerator();
        private readonly PrintService _printService = new PrintService();
        
        // UI Controls
        private TextBox _productNameTextBox = null!;
        private NumericUpDown _quantityNumericUpDown = null!;
        private TextBox _priceTextBox = null!;
        private Button _addButton = null!;
        private DataGridView _productsDataGridView = null!;
        private TextBox _customerNameTextBox = null!;
        private TextBox _notesTextBox = null!;
        private Label _totalLabel = null!;
        private Button _deleteButton = null!;
        private Button _clearButton = null!;
        private Button _generateInvoiceButton = null!;
        private CheckBox _includeTaxCheckBox = null!;
        private Button _settingsButton = null!;
        
        public MainFormFinal()
        {
            InitializeComponent();
            SetupEventHandlers();
            UpdateTotal();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "HokTech POS System - Direct Print A4 - Egyptian Pounds";
            this.Size = new Size(1300, 850);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1200, 800);
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(46, 134, 171),
                Padding = new Padding(20)
            };
            
            var titleLabel = new Label
            {
                Text = "HokTech POS System\nDirect Print A4 - Egyptian Pounds (EGP)",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            headerPanel.Controls.Add(titleLabel);

            // Settings button in header
            _settingsButton = new Button
            {
                Text = "⚙️\nSettings",
                Location = new Point(headerPanel.Width - 100, 10),
                Size = new Size(80, 60),
                Font = new Font("Arial", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false,
                Anchor = AnchorStyles.Top | AnchorStyles.Right
            };
            _settingsButton.FlatAppearance.BorderSize = 0;
            headerPanel.Controls.Add(_settingsButton);
            
            // Product Input Panel
            var inputPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 120,
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.White
            };
            
            var inputGroup = new GroupBox
            {
                Text = "Add Product / Service",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                ForeColor = Color.FromArgb(46, 134, 171)
            };
            
            // Product name
            var nameLabel = new Label
            {
                Text = "Product/Service Name:",
                Location = new Point(20, 35),
                Size = new Size(150, 23),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            inputGroup.Controls.Add(nameLabel);
            
            _productNameTextBox = new TextBox
            {
                Location = new Point(180, 32),
                Size = new Size(350, 25),
                Font = new Font("Arial", 11),
                PlaceholderText = "Enter product or service name..."
            };
            inputGroup.Controls.Add(_productNameTextBox);
            
            // Quantity
            var quantityLabel = new Label
            {
                Text = "Quantity:",
                Location = new Point(550, 35),
                Size = new Size(70, 23),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            inputGroup.Controls.Add(quantityLabel);
            
            _quantityNumericUpDown = new NumericUpDown
            {
                Location = new Point(630, 32),
                Size = new Size(80, 25),
                Font = new Font("Arial", 11),
                Minimum = 1,
                Maximum = 1000,
                Value = 1
            };
            inputGroup.Controls.Add(_quantityNumericUpDown);
            
            // Price
            var priceLabel = new Label
            {
                Text = "Price (EGP):",
                Location = new Point(730, 35),
                Size = new Size(80, 23),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            inputGroup.Controls.Add(priceLabel);
            
            _priceTextBox = new TextBox
            {
                Location = new Point(820, 32),
                Size = new Size(120, 25),
                Font = new Font("Arial", 11),
                PlaceholderText = "0.00"
            };
            inputGroup.Controls.Add(_priceTextBox);
            
            // Add button
            _addButton = new Button
            {
                Text = "Add Product",
                Location = new Point(960, 30),
                Size = new Size(130, 35),
                Font = new Font("Arial", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(241, 143, 1),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _addButton.FlatAppearance.BorderSize = 0;
            inputGroup.Controls.Add(_addButton);
            
            inputPanel.Controls.Add(inputGroup);
            
            // Products Table Panel
            var tablePanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.White
            };
            
            var tableGroup = new GroupBox
            {
                Text = "Products List",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(15),
                ForeColor = Color.FromArgb(46, 134, 171)
            };
            
            _productsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                RowHeadersVisible = false,
                ColumnHeadersHeight = 40,
                RowTemplate = { Height = 35 },
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(46, 134, 171),
                    ForeColor = Color.White,
                    Font = new Font("Arial", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(5)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Arial", 10),
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black,
                    Padding = new Padding(5)
                }
            };
            
            // Setup columns
            _productsDataGridView.Columns.Add("Product", "Product/Service");
            _productsDataGridView.Columns.Add("Quantity", "Qty");
            _productsDataGridView.Columns.Add("Price", "Unit Price (EGP)");
            _productsDataGridView.Columns.Add("Total", "Total (EGP)");
            
            // Set column widths
            _productsDataGridView.Columns[0].FillWeight = 50;
            _productsDataGridView.Columns[1].FillWeight = 15;
            _productsDataGridView.Columns[2].FillWeight = 20;
            _productsDataGridView.Columns[3].FillWeight = 20;
            
            // Align numeric columns
            _productsDataGridView.Columns[1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _productsDataGridView.Columns[2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _productsDataGridView.Columns[3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            
            tableGroup.Controls.Add(_productsDataGridView);
            tablePanel.Controls.Add(tableGroup);
            
            // Bottom Panel
            var bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 270, // Increased height for tax checkbox
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.FromArgb(250, 250, 250)
            };
            
            // Customer info panel
            var customerPanel = new GroupBox
            {
                Text = "Customer Information",
                Font = new Font("Arial", 11, FontStyle.Bold),
                Dock = DockStyle.Left,
                Width = 500,
                Padding = new Padding(15),
                ForeColor = Color.FromArgb(46, 134, 171)
            };
            
            var customerLabel = new Label
            {
                Text = "Customer Name:",
                Location = new Point(20, 35),
                Size = new Size(120, 25),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            customerPanel.Controls.Add(customerLabel);
            
            _customerNameTextBox = new TextBox
            {
                Location = new Point(20, 65),
                Size = new Size(450, 25),
                Font = new Font("Arial", 10),
                PlaceholderText = "Enter customer name (optional)..."
            };
            customerPanel.Controls.Add(_customerNameTextBox);
            
            var notesLabel = new Label
            {
                Text = "Notes:",
                Location = new Point(20, 105),
                Size = new Size(60, 25),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            customerPanel.Controls.Add(notesLabel);
            
            _notesTextBox = new TextBox
            {
                Location = new Point(20, 135),
                Size = new Size(450, 65),
                Font = new Font("Arial", 10),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "Enter notes or comments..."
            };
            customerPanel.Controls.Add(_notesTextBox);
            
            bottomPanel.Controls.Add(customerPanel);
            
            // Actions panel
            var actionsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 400,
                Padding = new Padding(30, 0, 0, 0)
            };
            
            // Total label
            _totalLabel = new Label
            {
                Text = "Total: 0.00 EGP",
                Font = new Font("Arial", 20, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Location = new Point(40, 25),
                Size = new Size(320, 45),
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            actionsPanel.Controls.Add(_totalLabel);
            
            // Buttons
            _deleteButton = new Button
            {
                Text = "Delete\nSelected Item",
                Location = new Point(40, 90),
                Size = new Size(120, 55),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _deleteButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_deleteButton);
            
            _clearButton = new Button
            {
                Text = "Clear\nAll Items",
                Location = new Point(180, 90),
                Size = new Size(120, 55),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _clearButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_clearButton);
            
            // Tax checkbox
            _includeTaxCheckBox = new CheckBox
            {
                Text = "Include VAT (15%)\nتضمين ضريبة القيمة المضافة",
                Location = new Point(40, 155),
                Size = new Size(260, 40),
                Font = new Font("Arial", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Checked = true, // Default to include tax
                TextAlign = ContentAlignment.MiddleLeft
            };
            actionsPanel.Controls.Add(_includeTaxCheckBox);

            _generateInvoiceButton = new Button
            {
                Text = "Print\nInvoice",
                Location = new Point(40, 200),
                Size = new Size(260, 55),
                Font = new Font("Arial", 14, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _generateInvoiceButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_generateInvoiceButton);
            
            bottomPanel.Controls.Add(actionsPanel);
            
            // Add all panels to form in correct order
            this.Controls.Add(tablePanel);      // Fill - must be added first
            this.Controls.Add(bottomPanel);     // Bottom
            this.Controls.Add(inputPanel);      // Top
            this.Controls.Add(headerPanel);     // Top
            
            this.ResumeLayout(false);
        }
        
        private void SetupEventHandlers()
        {
            _addButton.Click += AddButton_Click;
            _deleteButton.Click += DeleteButton_Click;
            _clearButton.Click += ClearButton_Click;
            _generateInvoiceButton.Click += GenerateInvoiceButton_Click;
            _settingsButton.Click += SettingsButton_Click;

            // Enter key navigation
            _productNameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _quantityNumericUpDown.Focus(); };
            _quantityNumericUpDown.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _priceTextBox.Focus(); };
            _priceTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) AddProduct(); };
        }
        
        private void AddButton_Click(object? sender, EventArgs e)
        {
            AddProduct();
        }
        
        private void AddProduct()
        {
            var name = _productNameTextBox.Text.Trim();
            var quantity = (int)_quantityNumericUpDown.Value;
            
            if (string.IsNullOrWhiteSpace(name))
            {
                MessageBox.Show("Please enter product/service name.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _productNameTextBox.Focus();
                return;
            }
            
            if (!decimal.TryParse(_priceTextBox.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("Please enter a valid price.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _priceTextBox.Focus();
                return;
            }
            
            var product = new Product(name, quantity, price);
            _products.Add(product);
            
            // Add to grid
            _productsDataGridView.Rows.Add(product.Name, product.Quantity, $"{product.Price:F2}", $"{product.Total:F2}");
            
            // Clear inputs
            _productNameTextBox.Clear();
            _quantityNumericUpDown.Value = 1;
            _priceTextBox.Clear();
            _productNameTextBox.Focus();
            
            UpdateTotal();
        }
        
        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (_productsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select an item to delete.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var selectedIndex = _productsDataGridView.SelectedRows[0].Index;
            _products.RemoveAt(selectedIndex);
            _productsDataGridView.Rows.RemoveAt(selectedIndex);
            
            UpdateTotal();
        }
        
        private void ClearButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0) return;
            
            var result = MessageBox.Show("Do you want to clear all items?", "Confirm",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _products.Clear();
                _productsDataGridView.Rows.Clear();
                UpdateTotal();
            }
        }
        
        private void GenerateInvoiceButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0)
            {
                MessageBox.Show("Please add products first.", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                var invoiceNumber = _invoiceGenerator.GetNextInvoiceNumber();
                var invoice = new Invoice(invoiceNumber, _customerNameTextBox.Text.Trim(), _notesTextBox.Text.Trim());

                // Set tax rate based on checkbox
                if (!_includeTaxCheckBox.Checked)
                {
                    invoice.TaxRate = 0; // No tax
                }

                foreach (var product in _products)
                {
                    invoice.AddProduct(product);
                }

                // Show print options
                var printChoice = MessageBox.Show(
                    "اختر طريقة الطباعة:\n\nYes = طباعة مباشرة (Direct Print)\nNo = اختيار الطابعة (Choose Printer)\nCancel = إلغاء",
                    "خيارات الطباعة - Print Options",
                    MessageBoxButtons.YesNoCancel,
                    MessageBoxIcon.Question);

                if (printChoice == DialogResult.Yes)
                {
                    // Direct print
                    _printService.PrintInvoiceDirectly(invoice);
                }
                else if (printChoice == DialogResult.No)
                {
                    // Print with dialog
                    _printService.PrintInvoice(invoice);
                }
                else
                {
                    return; // Cancel
                }

                var clearResult = MessageBox.Show("هل تريد مسح البيانات لفاتورة جديدة؟\nDo you want to clear data for a new invoice?",
                    "مسح البيانات - Clear Data",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (clearResult == DialogResult.Yes)
                {
                    _products.Clear();
                    _productsDataGridView.Rows.Clear();
                    _customerNameTextBox.Clear();
                    _notesTextBox.Clear();
                    UpdateTotal();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة:\nPrint Error:\n{ex.Message}", "خطأ - Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void SettingsButton_Click(object? sender, EventArgs e)
        {
            try
            {
                using (var settingsForm = new HokTechPOS.Forms.CompanySettingsForm())
                {
                    var result = settingsForm.ShowDialog(this);

                    if (result == DialogResult.OK)
                    {
                        // Settings were saved, show confirmation
                        MessageBox.Show("تم تحديث إعدادات الشركة بنجاح!\nCompany settings updated successfully!\n\nسيتم تطبيق التغييرات في الفواتير القادمة.\nChanges will be applied to future invoices.",
                            "تم التحديث - Updated",
                            MessageBoxButtons.OK,
                            MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الإعدادات:\nError opening settings window:\n{ex.Message}",
                    "خطأ - Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        private void UpdateTotal()
        {
            var total = _products.Sum(p => p.Total);
            _totalLabel.Text = $"Total: {total:F2} EGP";
        }
    }
}
