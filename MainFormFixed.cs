using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HokTechPOS.Models;
using HokTechPOS.Services;

namespace HokTechPOS
{
    public partial class MainFormFixed : Form
    {
        private readonly List<Product> _products = new List<Product>();
        private readonly InvoiceGenerator _invoiceGenerator = new InvoiceGenerator();
        
        // UI Controls
        private TextBox _productNameTextBox = null!;
        private NumericUpDown _quantityNumericUpDown = null!;
        private TextBox _priceTextBox = null!;
        private Button _addButton = null!;
        private DataGridView _productsDataGridView = null!;
        private TextBox _customerNameTextBox = null!;
        private TextBox _notesTextBox = null!;
        private Label _totalLabel = null!;
        private Button _deleteButton = null!;
        private Button _clearButton = null!;
        private Button _generateInvoiceButton = null!;
        
        public MainFormFixed()
        {
            InitializeComponent();
            SetupEventHandlers();
            UpdateTotal();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "HokTech POS System";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(1000, 700);
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(46, 134, 171),
                Padding = new Padding(20)
            };
            
            var titleLabel = new Label
            {
                Text = "HokTech POS System\nPoint of Sale",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
            
            // Product Input Panel
            var inputPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.White
            };
            
            var inputGroup = new GroupBox
            {
                Text = "Add Product / Service",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };
            
            // Product name
            var nameLabel = new Label
            {
                Text = "Product/Service Name:",
                Location = new Point(20, 30),
                Size = new Size(150, 23),
                Font = new Font("Arial", 10)
            };
            inputGroup.Controls.Add(nameLabel);
            
            _productNameTextBox = new TextBox
            {
                Location = new Point(180, 27),
                Size = new Size(300, 23),
                Font = new Font("Arial", 10),
                PlaceholderText = "Enter product or service name..."
            };
            inputGroup.Controls.Add(_productNameTextBox);
            
            // Quantity
            var quantityLabel = new Label
            {
                Text = "Quantity:",
                Location = new Point(500, 30),
                Size = new Size(60, 23),
                Font = new Font("Arial", 10)
            };
            inputGroup.Controls.Add(quantityLabel);
            
            _quantityNumericUpDown = new NumericUpDown
            {
                Location = new Point(570, 27),
                Size = new Size(80, 23),
                Font = new Font("Arial", 10),
                Minimum = 1,
                Maximum = 1000,
                Value = 1
            };
            inputGroup.Controls.Add(_quantityNumericUpDown);
            
            // Price
            var priceLabel = new Label
            {
                Text = "Price (EGP):",
                Location = new Point(670, 30),
                Size = new Size(80, 23),
                Font = new Font("Arial", 10)
            };
            inputGroup.Controls.Add(priceLabel);
            
            _priceTextBox = new TextBox
            {
                Location = new Point(760, 27),
                Size = new Size(100, 23),
                Font = new Font("Arial", 10),
                PlaceholderText = "0.00 EGP"
            };
            inputGroup.Controls.Add(_priceTextBox);
            
            // Add button
            _addButton = new Button
            {
                Text = "Add Product",
                Location = new Point(880, 25),
                Size = new Size(120, 30),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(241, 143, 1),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _addButton.FlatAppearance.BorderSize = 0;
            inputGroup.Controls.Add(_addButton);
            
            inputPanel.Controls.Add(inputGroup);
            this.Controls.Add(inputPanel);
            
            // Products Table Panel
            var tablePanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.White
            };

            var tableGroup = new GroupBox
            {
                Text = "Products List",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.White
            };
            
            _productsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(46, 134, 171),
                    ForeColor = Color.White,
                    Font = new Font("Arial", 10, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Arial", 9),
                    SelectionBackColor = Color.FromArgb(173, 216, 230),
                    SelectionForeColor = Color.Black
                }
            };
            
            // Setup columns
            _productsDataGridView.Columns.Add("Product", "Product/Service");
            _productsDataGridView.Columns.Add("Quantity", "Qty");
            _productsDataGridView.Columns.Add("Price", "Unit Price (EGP)");
            _productsDataGridView.Columns.Add("Total", "Total (EGP)");
            
            // Set column widths
            _productsDataGridView.Columns[0].FillWeight = 50;
            _productsDataGridView.Columns[1].FillWeight = 15;
            _productsDataGridView.Columns[2].FillWeight = 20;
            _productsDataGridView.Columns[3].FillWeight = 20;
            
            // Align numeric columns
            _productsDataGridView.Columns[1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _productsDataGridView.Columns[2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _productsDataGridView.Columns[3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            
            tableGroup.Controls.Add(_productsDataGridView);
            tablePanel.Controls.Add(tableGroup);
            this.Controls.Add(tablePanel);
            
            // Bottom Panel
            var bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 200,
                Padding = new Padding(20, 10, 20, 10),
                BackColor = Color.FromArgb(250, 250, 250)
            };
            
            // Customer info panel
            var customerPanel = new GroupBox
            {
                Text = "Customer Information",
                Font = new Font("Arial", 10, FontStyle.Bold),
                Dock = DockStyle.Left,
                Width = 450,
                Padding = new Padding(10)
            };
            
            var customerLabel = new Label
            {
                Text = "Customer Name:",
                Location = new Point(15, 30),
                Size = new Size(100, 23),
                Font = new Font("Arial", 9)
            };
            customerPanel.Controls.Add(customerLabel);
            
            _customerNameTextBox = new TextBox
            {
                Location = new Point(15, 55),
                Size = new Size(410, 23),
                Font = new Font("Arial", 9),
                PlaceholderText = "Enter customer name (optional)..."
            };
            customerPanel.Controls.Add(_customerNameTextBox);
            
            var notesLabel = new Label
            {
                Text = "Notes:",
                Location = new Point(15, 90),
                Size = new Size(50, 23),
                Font = new Font("Arial", 9)
            };
            customerPanel.Controls.Add(notesLabel);
            
            _notesTextBox = new TextBox
            {
                Location = new Point(15, 115),
                Size = new Size(410, 60),
                Font = new Font("Arial", 9),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                PlaceholderText = "Enter notes or comments..."
            };
            customerPanel.Controls.Add(_notesTextBox);
            
            bottomPanel.Controls.Add(customerPanel);
            
            // Actions panel
            var actionsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 350,
                Padding = new Padding(20, 0, 0, 0)
            };
            
            // Total label
            _totalLabel = new Label
            {
                Text = "Total: 0.00 EGP",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Location = new Point(30, 20),
                Size = new Size(300, 35),
                TextAlign = ContentAlignment.MiddleCenter,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            actionsPanel.Controls.Add(_totalLabel);
            
            // Buttons
            _deleteButton = new Button
            {
                Text = "Delete\nSelected Item",
                Location = new Point(30, 80),
                Size = new Size(100, 50),
                Font = new Font("Arial", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _deleteButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_deleteButton);
            
            _clearButton = new Button
            {
                Text = "Clear\nAll Items",
                Location = new Point(140, 80),
                Size = new Size(100, 50),
                Font = new Font("Arial", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _clearButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_clearButton);
            
            _generateInvoiceButton = new Button
            {
                Text = "Generate\nInvoice",
                Location = new Point(30, 140),
                Size = new Size(210, 50),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _generateInvoiceButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_generateInvoiceButton);
            
            bottomPanel.Controls.Add(actionsPanel);
            this.Controls.Add(bottomPanel);
            
            this.ResumeLayout(false);
        }
        
        private void SetupEventHandlers()
        {
            _addButton.Click += AddButton_Click;
            _deleteButton.Click += DeleteButton_Click;
            _clearButton.Click += ClearButton_Click;
            _generateInvoiceButton.Click += GenerateInvoiceButton_Click;
            
            // Enter key navigation
            _productNameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _quantityNumericUpDown.Focus(); };
            _quantityNumericUpDown.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _priceTextBox.Focus(); };
            _priceTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) AddProduct(); };
        }
        
        private void AddButton_Click(object? sender, EventArgs e)
        {
            AddProduct();
        }
        
        private void AddProduct()
        {
            var name = _productNameTextBox.Text.Trim();
            var quantity = (int)_quantityNumericUpDown.Value;
            
            if (string.IsNullOrWhiteSpace(name))
            {
                MessageBox.Show("Please enter product/service name.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _productNameTextBox.Focus();
                return;
            }
            
            if (!decimal.TryParse(_priceTextBox.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("Please enter a valid price.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _priceTextBox.Focus();
                return;
            }
            
            var product = new Product(name, quantity, price);
            _products.Add(product);
            
            // Add to grid
            _productsDataGridView.Rows.Add(product.Name, product.Quantity, $"{product.Price:F2}", $"{product.Total:F2}");
            
            // Clear inputs
            _productNameTextBox.Clear();
            _quantityNumericUpDown.Value = 1;
            _priceTextBox.Clear();
            _productNameTextBox.Focus();
            
            UpdateTotal();
        }
        
        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (_productsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select an item to delete.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            var selectedIndex = _productsDataGridView.SelectedRows[0].Index;
            _products.RemoveAt(selectedIndex);
            _productsDataGridView.Rows.RemoveAt(selectedIndex);
            
            UpdateTotal();
        }
        
        private void ClearButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0) return;
            
            var result = MessageBox.Show("Do you want to clear all items?", "Confirm",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _products.Clear();
                _productsDataGridView.Rows.Clear();
                UpdateTotal();
            }
        }
        
        private void GenerateInvoiceButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0)
            {
                MessageBox.Show("Please add products first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            try
            {
                var invoiceNumber = _invoiceGenerator.GetNextInvoiceNumber();
                var invoice = new Invoice(invoiceNumber, _customerNameTextBox.Text.Trim(), _notesTextBox.Text.Trim());
                
                foreach (var product in _products)
                {
                    invoice.AddProduct(product);
                }
                
                var filePath = _invoiceGenerator.GenerateInvoice(invoice);
                
                MessageBox.Show($"Invoice created successfully!\n\nFile: {System.IO.Path.GetFileName(filePath)}", 
                    "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                var openResult = MessageBox.Show("Do you want to open the invoice?", "Open Invoice", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (openResult == DialogResult.Yes)
                {
                    _invoiceGenerator.OpenInvoice(filePath);
                }
                
                var clearResult = MessageBox.Show("Do you want to clear data for a new invoice?", "Clear Data", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (clearResult == DialogResult.Yes)
                {
                    _products.Clear();
                    _productsDataGridView.Rows.Clear();
                    _customerNameTextBox.Clear();
                    _notesTextBox.Clear();
                    UpdateTotal();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating invoice:\n{ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdateTotal()
        {
            var total = _products.Sum(p => p.Total);
            _totalLabel.Text = $"Total: {total:F2} EGP";
        }
    }
}
