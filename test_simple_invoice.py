#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مولد الفواتير المبسط
Test Simple Invoice Generator
"""

from invoice_generator_simple import SimpleInvoiceGenerator
import os

def test_simple_invoice():
    """اختبار إنشاء فاتورة مبسطة"""
    
    # إنشاء مولد الفواتير
    generator = SimpleInvoiceGenerator()
    
    # بيانات تجريبية
    test_items = [
        {
            'name': 'Website Development Service',
            'quantity': 1,
            'price': 5000.00
        },
        {
            'name': 'Logo Design',
            'quantity': 2,
            'price': 500.00
        },
        {
            'name': 'Annual Hosting Service',
            'quantity': 1,
            'price': 800.00
        },
        {
            'name': 'Mobile App Development',
            'quantity': 1,
            'price': 8000.00
        }
    ]
    
    customer_name = "Advanced Technology Solutions Company"
    notes = """
    Thank you for choosing HokTech services.
    Payment is due within 30 days from invoice date.
    For inquiries, please contact us using the numbers above.
    We look forward to serving you again.
    """
    
    try:
        # إنشاء الفاتورة
        filepath = generator.generate_invoice(test_items, customer_name, notes)
        print(f"✅ تم إنشاء الفاتورة المبسطة بنجاح!")
        print(f"✅ Simple invoice created successfully!")
        print(f"📄 الملف: {filepath}")
        print(f"📄 File: {filepath}")
        
        # التحقق من وجود الملف
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ الملف موجود وحجمه: {file_size:,} بايت")
            print(f"✅ File exists with size: {file_size:,} bytes")
            
            return True
        else:
            print("❌ الملف غير موجود!")
            print("❌ File not found!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفاتورة: {str(e)}")
        print(f"❌ Error creating invoice: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 اختبار مولد الفواتير المبسط...")
    print("🧪 Testing Simple Invoice Generator...")
    print("=" * 60)
    
    success = test_simple_invoice()
    
    print("=" * 60)
    if success:
        print("🎉 اختبار المولد المبسط نجح!")
        print("🎉 Simple generator test passed!")
        print("📋 الفاتورة تحتوي على:")
        print("📋 Invoice contains:")
        print("   • شعار HokTech / HokTech Logo")
        print("   • نص إنجليزي واضح / Clear English Text") 
        print("   • تصميم احترافي / Professional Design")
        print("   • حسابات صحيحة / Accurate Calculations")
        print("   • معلومات الشركة / Company Information")
    else:
        print("💥 اختبار المولد المبسط فشل!")
        print("💥 Simple generator test failed!")
        print("🔧 يرجى التحقق من:")
        print("🔧 Please check:")
        print("   • تثبيت reportlab / reportlab installation")
        print("   • وجود ملف الشعار / Logo file existence")
        print("   • صلاحيات الكتابة / Write permissions")
