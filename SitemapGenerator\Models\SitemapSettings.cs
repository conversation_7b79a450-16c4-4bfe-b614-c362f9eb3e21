using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace SitemapGenerator.Models
{
    /// <summary>
    /// Configuration settings for the sitemap generator
    /// </summary>
    public class SitemapSettings
    {
        /// <summary>
        /// Website base URL
        /// </summary>
        public string BaseUrl { get; set; } = "https://example.com";

        /// <summary>
        /// Website name/title
        /// </summary>
        public string WebsiteName { get; set; } = "My Website";

        /// <summary>
        /// Default language for the website
        /// </summary>
        public string DefaultLanguage { get; set; } = "en";

        /// <summary>
        /// Supported languages
        /// </summary>
        public List<string> SupportedLanguages { get; set; } = new List<string> { "en", "ar" };

        /// <summary>
        /// Maximum number of URLs per sitemap file (Google limit: 50,000)
        /// </summary>
        public int MaxUrlsPerSitemap { get; set; } = 50000;

        /// <summary>
        /// Maximum file size for sitemap in MB (Google limit: 50MB)
        /// </summary>
        public int MaxSitemapSizeMB { get; set; } = 50;

        /// <summary>
        /// Output directory for generated sitemaps
        /// </summary>
        public string OutputDirectory { get; set; } = "sitemaps";

        /// <summary>
        /// Whether to generate robots.txt file
        /// </summary>
        public bool GenerateRobotsTxt { get; set; } = true;

        /// <summary>
        /// Whether to include image sitemaps
        /// </summary>
        public bool IncludeImageSitemap { get; set; } = true;

        /// <summary>
        /// Whether to include video sitemaps
        /// </summary>
        public bool IncludeVideoSitemap { get; set; } = true;

        /// <summary>
        /// Whether to include news sitemaps
        /// </summary>
        public bool IncludeNewsSitemap { get; set; } = false;

        /// <summary>
        /// Whether to compress sitemaps with gzip
        /// </summary>
        public bool CompressSitemaps { get; set; } = true;

        /// <summary>
        /// Default change frequency for new URLs
        /// </summary>
        public ChangeFrequency DefaultChangeFrequency { get; set; } = ChangeFrequency.Weekly;

        /// <summary>
        /// Default priority for new URLs
        /// </summary>
        public double DefaultPriority { get; set; } = 0.5;

        /// <summary>
        /// Crawling settings
        /// </summary>
        public CrawlSettings CrawlSettings { get; set; } = new CrawlSettings();

        /// <summary>
        /// SEO settings
        /// </summary>
        public SeoSettings SeoSettings { get; set; } = new SeoSettings();

        /// <summary>
        /// Google Search Console settings
        /// </summary>
        public GoogleSearchConsoleSettings GoogleSettings { get; set; } = new GoogleSearchConsoleSettings();

        private static readonly string SettingsFilePath = "sitemap_settings.json";

        /// <summary>
        /// Load settings from file
        /// </summary>
        public static SitemapSettings Load()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    var json = File.ReadAllText(SettingsFilePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true
                    };
                    return JsonSerializer.Deserialize<SitemapSettings>(json, options) ?? new SitemapSettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }
            
            return new SitemapSettings();
        }

        /// <summary>
        /// Save settings to file
        /// </summary>
        public void Save()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                var json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to save sitemap settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset to default settings
        /// </summary>
        public static void ResetToDefaults()
        {
            var defaultSettings = new SitemapSettings();
            defaultSettings.Save();
        }
    }

    /// <summary>
    /// Web crawling configuration
    /// </summary>
    public class CrawlSettings
    {
        /// <summary>
        /// Maximum depth to crawl
        /// </summary>
        public int MaxDepth { get; set; } = 5;

        /// <summary>
        /// Maximum number of pages to crawl
        /// </summary>
        public int MaxPages { get; set; } = 10000;

        /// <summary>
        /// Delay between requests in milliseconds
        /// </summary>
        public int DelayBetweenRequests { get; set; } = 1000;

        /// <summary>
        /// Request timeout in seconds
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// User agent string for crawling
        /// </summary>
        public string UserAgent { get; set; } = "SEO Sitemap Generator Bot 1.0";

        /// <summary>
        /// Whether to follow external links
        /// </summary>
        public bool FollowExternalLinks { get; set; } = false;

        /// <summary>
        /// Whether to respect robots.txt
        /// </summary>
        public bool RespectRobotsTxt { get; set; } = true;

        /// <summary>
        /// File extensions to include
        /// </summary>
        public List<string> IncludeExtensions { get; set; } = new List<string> 
        { 
            ".html", ".htm", ".php", ".asp", ".aspx", ".jsp", ".pdf" 
        };

        /// <summary>
        /// URL patterns to exclude
        /// </summary>
        public List<string> ExcludePatterns { get; set; } = new List<string>
        {
            "/admin/", "/wp-admin/", "/login", "/logout", "/search", "?print=", "#"
        };
    }

    /// <summary>
    /// SEO optimization settings
    /// </summary>
    public class SeoSettings
    {
        /// <summary>
        /// Whether to validate URLs before adding
        /// </summary>
        public bool ValidateUrls { get; set; } = true;

        /// <summary>
        /// Whether to check for duplicate content
        /// </summary>
        public bool CheckDuplicateContent { get; set; } = true;

        /// <summary>
        /// Whether to analyze page titles and descriptions
        /// </summary>
        public bool AnalyzeSeoElements { get; set; } = true;

        /// <summary>
        /// Whether to detect canonical URLs
        /// </summary>
        public bool DetectCanonicalUrls { get; set; } = true;

        /// <summary>
        /// Whether to include hreflang attributes for multi-language sites
        /// </summary>
        public bool IncludeHreflang { get; set; } = true;

        /// <summary>
        /// Minimum priority for URLs to include
        /// </summary>
        public double MinimumPriority { get; set; } = 0.1;
    }

    /// <summary>
    /// Google Search Console integration settings
    /// </summary>
    public class GoogleSearchConsoleSettings
    {
        /// <summary>
        /// Whether to enable Google Search Console integration
        /// </summary>
        public bool EnableIntegration { get; set; } = false;

        /// <summary>
        /// Google Search Console property URL
        /// </summary>
        public string PropertyUrl { get; set; } = string.Empty;

        /// <summary>
        /// Service account key file path for authentication
        /// </summary>
        public string ServiceAccountKeyPath { get; set; } = string.Empty;

        /// <summary>
        /// Whether to automatically submit sitemaps
        /// </summary>
        public bool AutoSubmitSitemaps { get; set; } = false;
    }
}
