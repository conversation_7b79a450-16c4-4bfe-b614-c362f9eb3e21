@echo off
echo ========================================
echo    HokTech POS System - Direct Print A4
echo    طباعة مباشرة على ورق A4
echo ========================================
echo.

echo Building the application...
dotnet build HokTechPOS.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the application...
    echo.
    echo Features:
    echo - Direct printing to A4 paper
    echo - Arabic text support in print
    echo - Egyptian Pounds (EGP) currency
    echo - Professional invoice layout
    echo.
    echo Instructions:
    echo 1. Add products (Arabic or English)
    echo 2. Click "Print Invoice"
    echo 3. Choose print option:
    echo    - Direct Print: Print immediately
    echo    - Choose Printer: Select printer first
    echo.
    dotnet run --project HokTechPOS.csproj --configuration Release
) else (
    echo.
    echo Build failed! Please check the errors above.
    pause
)
