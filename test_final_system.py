#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final System Test - Complete English Interface
اختبار النظام النهائي - واجهة إنجليزية كاملة
"""

from invoice_generator_english import EnglishInvoiceGenerator
import os

def test_final_system():
    """Test the complete English system"""
    
    print("🧪 Testing Final English POS System...")
    print("🧪 اختبار نظام نقاط البيع الإنجليزي النهائي...")
    print("=" * 60)
    
    # Create invoice generator
    generator = EnglishInvoiceGenerator()
    
    # Test data with English product names
    test_items = [
        {
            'name': 'Website Development Service',
            'quantity': 1,
            'price': 5000.00
        },
        {
            'name': 'Logo Design Package',
            'quantity': 1,
            'price': 1500.00
        },
        {
            'name': 'Mobile App Development',
            'quantity': 1,
            'price': 8000.00
        },
        {
            'name': 'Digital Marketing Package',
            'quantity': 3,
            'price': 2000.00
        },
        {
            'name': 'Technical Support (Annual)',
            'quantity': 1,
            'price': 3000.00
        }
    ]
    
    customer_name = "Advanced Technology Solutions Company"
    notes = """
    Thank you for choosing HokTech for your technology needs.
    Payment terms: Net 30 days from invoice date.
    
    Services include:
    - Full project management
    - Quality assurance testing
    - 6 months free support
    - Training for your team
    
    For any questions, please contact our support team.
    We look forward to a successful partnership!
    """
    
    try:
        # Generate invoice
        filepath = generator.generate_invoice(test_items, customer_name, notes)
        
        print("✅ Final system test PASSED!")
        print("✅ اختبار النظام النهائي نجح!")
        print(f"📄 Invoice file: {filepath}")
        print(f"📄 ملف الفاتورة: {filepath}")
        
        # Check file
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ File size: {file_size:,} bytes")
            print(f"✅ حجم الملف: {file_size:,} بايت")
            
            # Calculate totals for verification
            subtotal = sum(item['quantity'] * item['price'] for item in test_items)
            tax = subtotal * 0.15
            total = subtotal + tax
            
            print("\n📊 Invoice Summary:")
            print("📊 ملخص الفاتورة:")
            print(f"   • Items: {len(test_items)} products/services")
            print(f"   • العناصر: {len(test_items)} منتج/خدمة")
            print(f"   • Subtotal: {subtotal:,.2f} SAR")
            print(f"   • المجموع الفرعي: {subtotal:,.2f} ريال")
            print(f"   • VAT (15%): {tax:,.2f} SAR")
            print(f"   • ضريبة القيمة المضافة: {tax:,.2f} ريال")
            print(f"   • Total: {total:,.2f} SAR")
            print(f"   • الإجمالي: {total:,.2f} ريال")
            
            return True
        else:
            print("❌ File not found!")
            print("❌ الملف غير موجود!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        print(f"❌ فشل الاختبار: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_features():
    """Test system features"""
    print("\n🔍 System Features Test:")
    print("🔍 اختبار ميزات النظام:")
    
    features = [
        "✅ English-only interface (no Arabic text issues)",
        "✅ واجهة إنجليزية فقط (لا مشاكل في النص العربي)",
        "✅ Professional invoice design",
        "✅ تصميم فاتورة احترافي",
        "✅ Automatic calculations (subtotal, VAT, total)",
        "✅ حسابات تلقائية (المجموع الفرعي، الضريبة، الإجمالي)",
        "✅ HokTech branding and logo",
        "✅ علامة هوك تك والشعار",
        "✅ Customer information support",
        "✅ دعم معلومات العميل",
        "✅ Notes and comments",
        "✅ الملاحظات والتعليقات",
        "✅ PDF generation with clear text",
        "✅ إنشاء PDF بنص واضح",
        "✅ Professional layout and formatting",
        "✅ تخطيط وتنسيق احترافي"
    ]
    
    for feature in features:
        print(f"   {feature}")

if __name__ == "__main__":
    success = test_final_system()
    test_features()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 FINAL SYSTEM TEST PASSED!")
        print("🎉 اختبار النظام النهائي نجح!")
        print("\n🚀 System is ready for production use!")
        print("🚀 النظام جاهز للاستخدام التجاري!")
        print("\n📋 How to use:")
        print("📋 كيفية الاستخدام:")
        print("   1. Run: python main.py")
        print("   1. شغل: python main.py")
        print("   2. Add products with English names")
        print("   2. أضف منتجات بأسماء إنجليزية")
        print("   3. Enter customer information")
        print("   3. أدخل معلومات العميل")
        print("   4. Generate professional invoice")
        print("   4. أنشئ فاتورة احترافية")
    else:
        print("💥 FINAL SYSTEM TEST FAILED!")
        print("💥 فشل اختبار النظام النهائي!")
        print("🔧 Please check the error messages above")
        print("🔧 يرجى مراجعة رسائل الخطأ أعلاه")
