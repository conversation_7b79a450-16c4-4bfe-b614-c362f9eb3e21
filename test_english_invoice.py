#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test English Invoice Generator
اختبار مولد الفواتير الإنجليزي
"""

from invoice_generator_english import EnglishInvoiceGenerator
import os

def test_english_invoice():
    """Test creating English invoice"""
    
    # Create invoice generator
    generator = EnglishInvoiceGenerator()
    
    # Test data
    test_items = [
        {
            'name': 'Website Development Service',
            'quantity': 1,
            'price': 5000.00
        },
        {
            'name': 'Logo Design Package',
            'quantity': 2,
            'price': 500.00
        },
        {
            'name': 'Annual Hosting Service',
            'quantity': 1,
            'price': 800.00
        },
        {
            'name': 'Mobile App Development',
            'quantity': 1,
            'price': 8000.00
        }
    ]
    
    customer_name = "Advanced Technology Solutions Company"
    notes = """
    Thank you for choosing HokTech services.
    Payment is due within 30 days from invoice date.
    For inquiries, please contact us using the numbers above.
    We look forward to serving you again.
    """
    
    try:
        # Generate invoice
        filepath = generator.generate_invoice(test_items, customer_name, notes)
        print(f"✅ English invoice created successfully!")
        print(f"✅ تم إنشاء الفاتورة الإنجليزية بنجاح!")
        print(f"📄 File: {filepath}")
        print(f"📄 الملف: {filepath}")
        
        # Check if file exists
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ File exists with size: {file_size:,} bytes")
            print(f"✅ الملف موجود وحجمه: {file_size:,} بايت")
            
            return True
        else:
            print("❌ File not found!")
            print("❌ الملف غير موجود!")
            return False
            
    except Exception as e:
        print(f"❌ Error creating invoice: {str(e)}")
        print(f"❌ خطأ في إنشاء الفاتورة: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 Testing English Invoice Generator...")
    print("🧪 اختبار مولد الفواتير الإنجليزي...")
    print("=" * 60)
    
    success = test_english_invoice()
    
    print("=" * 60)
    if success:
        print("🎉 English invoice test passed!")
        print("🎉 اختبار الفاتورة الإنجليزية نجح!")
        print("📋 Invoice features:")
        print("📋 ميزات الفاتورة:")
        print("   • HokTech Logo / شعار هوك تك")
        print("   • Clear English Text / نص إنجليزي واضح") 
        print("   • Professional Design / تصميم احترافي")
        print("   • Accurate Calculations / حسابات دقيقة")
        print("   • Company Information / معلومات الشركة")
        print("   • NO Arabic Text Issues / لا مشاكل في النص العربي")
    else:
        print("💥 English invoice test failed!")
        print("💥 اختبار الفاتورة الإنجليزية فشل!")
        print("🔧 Please check:")
        print("🔧 يرجى التحقق من:")
        print("   • ReportLab installation / تثبيت ReportLab")
        print("   • Logo file existence / وجود ملف الشعار")
        print("   • Write permissions / صلاحيات الكتابة")
