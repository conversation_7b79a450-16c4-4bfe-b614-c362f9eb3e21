using System;
using System.IO;
using System.Text.Json;

namespace HokTechPOS.Models
{
    public class CompanySettings
    {
        public string CompanyName { get; set; } = "HokTech";
        public string Country { get; set; } = "Saudi Arabia";
        public string Phone { get; set; } = "+966 XX XXX XXXX";
        public string Email { get; set; } = "<EMAIL>";
        public string Website { get; set; } = "www.hoktech.com";
        public string ThankYouMessage { get; set; } = "Thank you for your business!";
        
        // Arabic versions
        public string CompanyNameArabic { get; set; } = "هوك تك";
        public string CountryArabic { get; set; } = "المملكة العربية السعودية";
        public string ThankYouMessageArabic { get; set; } = "شكراً لاختياركم خدماتنا!";
        
        private static readonly string SettingsFilePath = "company_settings.json";
        
        public static CompanySettings Load()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    string json = File.ReadAllText(SettingsFilePath);
                    var settings = JsonSerializer.Deserialize<CompanySettings>(json);
                    return settings ?? new CompanySettings();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
            }
            
            return new CompanySettings();
        }
        
        public void Save()
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };
                
                string json = JsonSerializer.Serialize(this, options);
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
                throw new Exception($"Failed to save company settings: {ex.Message}");
            }
        }
        
        public static void ResetToDefaults()
        {
            var defaultSettings = new CompanySettings();
            defaultSettings.Save();
        }
    }
}
