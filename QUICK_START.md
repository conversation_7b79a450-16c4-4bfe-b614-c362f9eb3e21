# دليل البدء السريع - HokTech POS
# Quick Start Guide - HokTech POS

## تشغيل التطبيق / Running the App

### الطريقة الأولى / Method 1:
```bash
python main.py
```

### الطريقة الثانية / Method 2:
انقر مرتين على ملف `run_pos.bat`
Double-click on `run_pos.bat`

## خطوات سريعة / Quick Steps

### 1. إضافة منتج / Add Product
- اسم المنتج: "خدمة تطوير"
- الكمية: 1
- السعر: 1000
- اضغط "إضافة"

### 2. إنشاء فاتورة / Create Invoice
- أدخل اسم العميل (اختياري)
- اضغط "إنشاء فاتورة"
- ستجد الفاتورة في مجلد `invoices/`

## اختصارات لوحة المفاتيح / Keyboard Shortcuts
- **Enter** في حقل اسم المنتج → الانتقال للكمية
- **Enter** في حقل الكمية → الانتقال للسعر  
- **Enter** في حقل السعر → إضافة المنتج

## مجلد الفواتير / Invoices Folder
```
invoices/
├── Invoice-0001.pdf
├── Invoice-0002.pdf
└── ...
```

## نصائح / Tips
✅ تأكد من إدخال أسعار صحيحة (أرقام فقط)
✅ يمكنك إضافة عدة منتجات قبل إنشاء الفاتورة
✅ الضريبة تحسب تلقائياً (15%)
✅ الفواتير تحفظ تلقائياً برقم متسلسل

## مشاكل شائعة / Common Issues

### المشكلة: التطبيق لا يفتح
**الحل:**
```bash
pip install reportlab Pillow
python main.py
```

### المشكلة: خطأ في إنشاء PDF
**الحل:** تأكد من وجود مجلد `invoices/` أو سيتم إنشاؤه تلقائياً

---
**🚀 جاهز للاستخدام! Ready to use!**
