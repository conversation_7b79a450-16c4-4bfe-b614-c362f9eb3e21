@echo off
echo ========================================
echo    HokTech POS - Company Settings Version
echo    نسخة مع إعدادات الشركة القابلة للتخصيص
echo ========================================
echo.

echo Stopping any running instances...
taskkill /f /im HokTechPOS.exe 2>nul
timeout /t 2 /nobreak >nul

echo Building the application...
dotnet build HokTechPOS.csproj --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful! Starting the COMPANY SETTINGS version...
    echo.
    echo ✅ NEW FEATURE ADDED:
    echo    - Company Settings Window (⚙️ Settings button)
    echo    - نافذة إعدادات الشركة قابلة للتخصيص
    echo    - Customizable company information
    echo    - Settings saved automatically for future use
    echo.
    echo Customizable Settings:
    echo ✅ Company Name (English + Arabic)
    echo ✅ Country (English + Arabic)
    echo ✅ Phone Number
    echo ✅ Email Address
    echo ✅ Website
    echo ✅ Thank You Message (English + Arabic)
    echo.
    echo Features:
    echo ✅ Direct printing to A4 paper
    echo ✅ Clean Arabic text (no Arabic suffix)
    echo ✅ HokTech logo in printed invoice
    echo ✅ Tax option checkbox
    echo ✅ Company settings customization (NEW!)
    echo ✅ Egyptian Pounds (EGP) currency
    echo ✅ Professional invoice layout
    echo.
    echo How to Use Settings:
    echo 1. Click ⚙️ Settings button in top-right corner
    echo 2. Customize company information
    echo 3. Click "Save Settings" / "حفظ الإعدادات"
    echo 4. Settings will be used in all future invoices
    echo 5. Settings are saved in "company_settings.json"
    echo.
    echo Test Instructions:
    echo 1. Click ⚙️ Settings and customize company info
    echo 2. Add product and print invoice
    echo 3. Verify custom company info appears in footer
    echo.
    dotnet run --project HokTechPOS.csproj --configuration Release
) else (
    echo.
    echo Build failed! Please check the errors above.
    pause
)
