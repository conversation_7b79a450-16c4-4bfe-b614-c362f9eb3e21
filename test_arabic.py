#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النص العربي في الفواتير
Test Arabic Text in Invoices
"""

from invoice_generator import InvoiceGenerator
import os

def test_arabic_invoice():
    """اختبار إنشاء فاتورة بنص عربي"""
    
    # إنشاء مولد الفواتير
    generator = InvoiceGenerator()
    
    # بيانات تجريبية بنص عربي
    test_items = [
        {
            'name': 'تطوير موقع إلكتروني متقدم',
            'quantity': 1,
            'price': 8000.00
        },
        {
            'name': 'تصميم هوية بصرية شاملة',
            'quantity': 1,
            'price': 3000.00
        },
        {
            'name': 'خدمة الاستضافة والدعم الفني',
            'quantity': 12,
            'price': 200.00
        },
        {
            'name': 'تطبيق جوال للأندرويد والآيفون',
            'quantity': 1,
            'price': 12000.00
        }
    ]
    
    customer_name = "شركة التقنية المتطورة للحلول الرقمية"
    notes = """
    شكراً لثقتكم في خدماتنا.
    يرجى السداد خلال 30 يوم من تاريخ الفاتورة.
    للاستفسارات يرجى التواصل معنا على الأرقام المذكورة أعلاه.
    نتطلع لخدمتكم مرة أخرى.
    """
    
    try:
        # إنشاء الفاتورة
        filepath = generator.generate_invoice(test_items, customer_name, notes)
        print(f"✅ تم إنشاء فاتورة النص العربي بنجاح!")
        print(f"✅ Arabic text invoice created successfully!")
        print(f"📄 الملف: {filepath}")
        print(f"📄 File: {filepath}")
        
        # التحقق من وجود الملف
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ الملف موجود وحجمه: {file_size:,} بايت")
            print(f"✅ File exists with size: {file_size:,} bytes")
            
            # اختبار معالجة النص العربي
            test_text = "مرحباً بكم في هوك تك"
            processed = generator.process_arabic_text(test_text)
            print(f"🔤 النص الأصلي: {test_text}")
            print(f"🔤 النص المعالج: {processed}")
            
            return True
        else:
            print("❌ الملف غير موجود!")
            print("❌ File not found!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفاتورة: {str(e)}")
        print(f"❌ Error creating invoice: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 اختبار النص العربي في الفواتير...")
    print("🧪 Testing Arabic Text in Invoices...")
    print("=" * 60)
    
    success = test_arabic_invoice()
    
    print("=" * 60)
    if success:
        print("🎉 اختبار النص العربي نجح! الفواتير تدعم النص العربي الآن")
        print("🎉 Arabic text test passed! Invoices now support Arabic text")
        print("📋 يمكنك الآن استخدام النص العربي في:")
        print("📋 You can now use Arabic text in:")
        print("   • أسماء المنتجات / Product names")
        print("   • أسماء العملاء / Customer names") 
        print("   • الملاحظات / Notes")
        print("   • جميع النصوص / All text fields")
    else:
        print("💥 اختبار النص العربي فشل!")
        print("💥 Arabic text test failed!")
        print("🔧 يرجى التحقق من تثبيت المكتبات:")
        print("🔧 Please check library installation:")
        print("   pip install arabic-reshaper python-bidi")
