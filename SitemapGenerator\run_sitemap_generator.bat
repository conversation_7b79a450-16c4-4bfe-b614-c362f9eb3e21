@echo off
echo ========================================
echo    SEO Sitemap Generator - HokTech
echo    مولد خرائط المواقع للسيو - هوك تك
echo ========================================
echo.

echo Checking .NET installation...
echo فحص تثبيت .NET...
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: .NET 6.0 or later is required!
    echo خطأ: يتطلب .NET 6.0 أو أحدث!
    echo.
    echo Please download and install .NET from:
    echo يرجى تحميل وتثبيت .NET من:
    echo https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET found! Building application...
echo تم العثور على .NET! بناء التطبيق...
echo.

echo Restoring packages...
echo استعادة الحزم...
dotnet restore
if %errorlevel% neq 0 (
    echo ERROR: Failed to restore packages!
    echo خطأ: فشل في استعادة الحزم!
    pause
    exit /b 1
)

echo Building project...
echo بناء المشروع...
dotnet build SitemapGenerator.csproj --configuration Release
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    echo خطأ: فشل في البناء!
    echo.
    echo Please check the error messages above.
    echo يرجى مراجعة رسائل الخطأ أعلاه.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build successful! Starting application...
echo البناء نجح! بدء تشغيل التطبيق...
echo ========================================
echo.

dotnet run --project SitemapGenerator.csproj --configuration Release

if %errorlevel% neq 0 (
    echo.
    echo Application exited with error code: %errorlevel%
    echo التطبيق خرج برمز خطأ: %errorlevel%
    pause
)

echo.
echo Application closed.
echo تم إغلاق التطبيق.
pause
