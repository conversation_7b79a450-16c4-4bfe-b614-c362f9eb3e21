using System;
using System.Drawing;
using System.Drawing.Printing;
using System.Windows.Forms;
using System.IO;
using HokTechPOS.Models;
using System.Collections.Generic;

namespace HokTechPOS.Services
{
    public class PrintService
    {
        private Invoice? _currentInvoice;
        private Font? _titleFont;
        private Font? _headerFont;
        private Font? _normalFont;
        private Font? _boldFont;
        private Font? _smallFont;
        
        public PrintService()
        {
            InitializeFonts();
        }
        
        private void InitializeFonts()
        {
            _titleFont = new Font("Arial", 18, FontStyle.Bold);
            _headerFont = new Font("Arial", 14, FontStyle.Bold);
            _normalFont = new Font("Arial", 10, FontStyle.Regular);
            _boldFont = new Font("Arial", 10, FontStyle.Bold);
            _smallFont = new Font("Arial", 8, FontStyle.Regular);
        }
        
        public void PrintInvoice(Invoice invoice)
        {
            _currentInvoice = invoice;
            
            try
            {
                PrintDocument printDoc = new PrintDocument();
                printDoc.PrintPage += PrintDoc_PrintPage;
                
                // Set paper size to A4
                printDoc.DefaultPageSettings.PaperSize = new PaperSize("A4", 827, 1169);
                printDoc.DefaultPageSettings.Margins = new Margins(50, 50, 50, 50);
                
                // Show print dialog
                PrintDialog printDialog = new PrintDialog();
                printDialog.Document = printDoc;
                
                if (printDialog.ShowDialog() == DialogResult.OK)
                {
                    printDoc.Print();
                    MessageBox.Show("تم طباعة الفاتورة بنجاح!", "نجح الطباعة", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        public void PrintInvoiceDirectly(Invoice invoice)
        {
            _currentInvoice = invoice;
            
            try
            {
                PrintDocument printDoc = new PrintDocument();
                printDoc.PrintPage += PrintDoc_PrintPage;
                
                // Set paper size to A4
                printDoc.DefaultPageSettings.PaperSize = new PaperSize("A4", 827, 1169);
                printDoc.DefaultPageSettings.Margins = new Margins(50, 50, 50, 50);
                
                // Print directly without dialog
                printDoc.Print();
                MessageBox.Show("تم طباعة الفاتورة بنجاح!", "نجح الطباعة", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void PrintDoc_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (_currentInvoice == null || e.Graphics == null) return;

            Graphics g = e.Graphics;
            float yPos = e.MarginBounds.Top;
            float leftMargin = e.MarginBounds.Left;
            float rightMargin = e.MarginBounds.Right;
            float pageWidth = e.MarginBounds.Width;
            
            // Company Header
            yPos = DrawHeader(g, leftMargin, rightMargin, yPos, pageWidth);
            yPos += 20;
            
            // Invoice Info
            yPos = DrawInvoiceInfo(g, leftMargin, rightMargin, yPos, pageWidth);
            yPos += 20;
            
            // Customer Info
            if (!string.IsNullOrWhiteSpace(_currentInvoice.CustomerName))
            {
                yPos = DrawCustomerInfo(g, leftMargin, yPos);
                yPos += 15;
            }
            
            // Products Table
            yPos = DrawProductsTable(g, leftMargin, rightMargin, yPos, pageWidth);
            yPos += 20;
            
            // Totals
            yPos = DrawTotals(g, rightMargin, yPos);
            yPos += 20;
            
            // Notes
            if (!string.IsNullOrWhiteSpace(_currentInvoice.Notes))
            {
                yPos = DrawNotes(g, leftMargin, yPos, pageWidth);
                yPos += 20;
            }
            
            // Footer
            DrawFooter(g, leftMargin, rightMargin, e.MarginBounds.Bottom - 100, pageWidth);
        }
        
        private float DrawHeader(Graphics g, float leftMargin, float rightMargin, float yPos, float pageWidth)
        {
            // Try to load and draw logo
            try
            {
                string logoPath = "HOKTECH Logo Design with Circuit Elements.png";
                if (File.Exists(logoPath))
                {
                    using (var logo = System.Drawing.Image.FromFile(logoPath))
                    {
                        // Calculate logo size (maintain aspect ratio)
                        float logoHeight = 60;
                        float logoWidth = (logo.Width * logoHeight) / logo.Height;
                        float logoX = leftMargin + (pageWidth - logoWidth) / 2;

                        g.DrawImage(logo, logoX, yPos, logoWidth, logoHeight);
                        yPos += logoHeight + 10;
                    }
                }
            }
            catch
            {
                // If logo fails to load, show company name instead
                string companyName = "HokTech";
                SizeF companySize = g.MeasureString(companyName, _titleFont ?? _normalFont);
                float companyX = leftMargin + (pageWidth - companySize.Width) / 2;
                g.DrawString(companyName, _titleFont ?? _normalFont, Brushes.Blue, companyX, yPos);
                yPos += companySize.Height + 10;
            }
            
            // Invoice Title
            string invoiceTitle = "INVOICE";
            SizeF titleSize = g.MeasureString(invoiceTitle, _headerFont);
            float titleX = leftMargin + (pageWidth - titleSize.Width) / 2;
            g.DrawString(invoiceTitle, _headerFont, Brushes.Black, titleX, yPos);
            yPos += titleSize.Height + 10;
            
            // Draw line
            g.DrawLine(Pens.Black, leftMargin, yPos, rightMargin, yPos);
            
            return yPos + 10;
        }
        
        private float DrawInvoiceInfo(Graphics g, float leftMargin, float rightMargin, float yPos, float pageWidth)
        {
            float lineHeight = _normalFont.GetHeight(g);
            
            // Invoice Number
            string invoiceNum = $"Invoice Number: {_currentInvoice.InvoiceNumber:D4}";
            g.DrawString(invoiceNum, _normalFont, Brushes.Black, leftMargin, yPos);
            
            // Date
            string date = $"Date: {_currentInvoice.Date:yyyy-MM-dd}";
            SizeF dateSize = g.MeasureString(date, _normalFont);
            g.DrawString(date, _normalFont, Brushes.Black, rightMargin - dateSize.Width, yPos);
            yPos += lineHeight;
            
            // Time
            string time = $"Time: {_currentInvoice.Date:HH:mm}";
            g.DrawString(time, _normalFont, Brushes.Black, leftMargin, yPos);
            
            return yPos + lineHeight;
        }
        
        private float DrawCustomerInfo(Graphics g, float leftMargin, float yPos)
        {
            string customer = $"Customer: {_currentInvoice.CustomerName}";
            g.DrawString(customer, _boldFont, Brushes.Black, leftMargin, yPos);
            return yPos + _boldFont.GetHeight(g);
        }
        
        private float DrawProductsTable(Graphics g, float leftMargin, float rightMargin, float yPos, float pageWidth)
        {
            float lineHeight = _normalFont.GetHeight(g) + 5;
            float headerHeight = _boldFont.GetHeight(g) + 10;
            
            // Column widths
            float col1Width = pageWidth * 0.5f;  // Product/Service
            float col2Width = pageWidth * 0.15f; // Quantity
            float col3Width = pageWidth * 0.175f; // Unit Price
            float col4Width = pageWidth * 0.175f; // Total
            
            // Header background
            RectangleF headerRect = new RectangleF(leftMargin, yPos, pageWidth, headerHeight);
            g.FillRectangle(new SolidBrush(Color.FromArgb(46, 134, 171)), headerRect);
            
            // Header text
            g.DrawString("Product/Service", _boldFont, Brushes.White, leftMargin + 5, yPos + 5);
            g.DrawString("Qty", _boldFont, Brushes.White, leftMargin + col1Width + 5, yPos + 5);
            g.DrawString("Unit Price (EGP)", _boldFont, Brushes.White, leftMargin + col1Width + col2Width + 5, yPos + 5);
            g.DrawString("Total (EGP)", _boldFont, Brushes.White, leftMargin + col1Width + col2Width + col3Width + 5, yPos + 5);
            
            yPos += headerHeight;
            
            // Draw header border
            g.DrawRectangle(Pens.Black, leftMargin, yPos - headerHeight, pageWidth, headerHeight);
            
            // Products
            foreach (var product in _currentInvoice.Products)
            {
                // Handle Arabic text
                string productName = TransliterateArabicText(product.Name);
                
                g.DrawString(productName, _normalFont, Brushes.Black, leftMargin + 5, yPos + 2);
                g.DrawString(product.Quantity.ToString(), _normalFont, Brushes.Black, leftMargin + col1Width + 5, yPos + 2);
                g.DrawString($"{product.Price:F2}", _normalFont, Brushes.Black, leftMargin + col1Width + col2Width + 5, yPos + 2);
                g.DrawString($"{product.Total:F2}", _normalFont, Brushes.Black, leftMargin + col1Width + col2Width + col3Width + 5, yPos + 2);
                
                yPos += lineHeight;
                
                // Draw row border
                g.DrawLine(Pens.Gray, leftMargin, yPos, rightMargin, yPos);
            }
            
            // Draw table border
            g.DrawRectangle(Pens.Black, leftMargin, yPos - (_currentInvoice.Products.Count * lineHeight), pageWidth, _currentInvoice.Products.Count * lineHeight);
            
            return yPos;
        }
        
        private float DrawTotals(Graphics g, float rightMargin, float yPos)
        {
            if (_currentInvoice == null) return yPos;

            float lineHeight = (_normalFont?.GetHeight(g) ?? 12) + 5;
            float totalWidth = 200;
            float leftPos = rightMargin - totalWidth;

            // Subtotal
            g.DrawString("Subtotal:", _normalFont ?? SystemFonts.DefaultFont, Brushes.Black, leftPos, yPos);
            g.DrawString($"{_currentInvoice.Subtotal:F2} EGP", _normalFont ?? SystemFonts.DefaultFont, Brushes.Black, leftPos + 100, yPos);
            yPos += lineHeight;

            // VAT (only if tax rate > 0)
            if (_currentInvoice.TaxRate > 0)
            {
                g.DrawString($"VAT ({_currentInvoice.TaxRate:P0}):", _normalFont ?? SystemFonts.DefaultFont, Brushes.Black, leftPos, yPos);
                g.DrawString($"{_currentInvoice.TaxAmount:F2} EGP", _normalFont ?? SystemFonts.DefaultFont, Brushes.Black, leftPos + 100, yPos);
                yPos += lineHeight;
            }

            // Total
            RectangleF totalRect = new RectangleF(leftPos, yPos, totalWidth, lineHeight + 5);
            g.FillRectangle(new SolidBrush(Color.FromArgb(46, 134, 171)), totalRect);
            g.DrawString("Total:", _boldFont ?? SystemFonts.DefaultFont, Brushes.White, leftPos + 5, yPos + 2);
            g.DrawString($"{_currentInvoice.Total:F2} EGP", _boldFont ?? SystemFonts.DefaultFont, Brushes.White, leftPos + 105, yPos + 2);

            return yPos + lineHeight + 5;
        }
        
        private float DrawNotes(Graphics g, float leftMargin, float yPos, float pageWidth)
        {
            g.DrawString("Notes:", _boldFont, Brushes.Black, leftMargin, yPos);
            yPos += _boldFont.GetHeight(g) + 5;
            
            // Wrap notes text
            RectangleF notesRect = new RectangleF(leftMargin, yPos, pageWidth, 100);
            g.DrawString(_currentInvoice.Notes, _normalFont, Brushes.Black, notesRect);
            
            return yPos + 60;
        }
        
        private void DrawFooter(Graphics g, float leftMargin, float rightMargin, float yPos, float pageWidth)
        {
            // Load company settings
            var settings = CompanySettings.Load();

            string footer = $"{settings.CompanyName}\n" +
                           $"{settings.Country}\n" +
                           $"Phone: {settings.Phone} | Email: {settings.Email}\n" +
                           $"Website: {settings.Website}\n\n" +
                           $"{settings.ThankYouMessage}";

            RectangleF footerRect = new RectangleF(leftMargin, yPos, pageWidth, 100);
            StringFormat centerFormat = new StringFormat();
            centerFormat.Alignment = StringAlignment.Center;

            g.DrawString(footer, _smallFont ?? SystemFonts.DefaultFont, Brushes.Gray, footerRect, centerFormat);
        }
        
        // Helper method to handle Arabic text (same as in InvoiceGenerator)
        private string TransliterateArabicText(string arabicText)
        {
            if (string.IsNullOrWhiteSpace(arabicText))
                return "Product/Service";
            
            var transliterations = new Dictionary<string, string>
            {
                {"تطبيقات", "Applications"},
                {"تطبيق", "Application"},
                {"تطوير", "Development"},
                {"موقع", "Website"},
                {"إلكتروني", "Electronic"},
                {"تصميم", "Design"},
                {"شعار", "Logo"},
                {"احترافي", "Professional"},
                {"دعم", "Support"},
                {"تقني", "Technical"},
                {"شامل", "Comprehensive"},
                {"خدمة", "Service"},
                {"خدمات", "Services"},
                {"برمجة", "Programming"},
                {"نظام", "System"},
                {"إدارة", "Management"},
                {"قاعدة", "Database"},
                {"بيانات", "Data"},
                {"أمان", "Security"},
                {"حماية", "Protection"},
                {"استشارة", "Consultation"},
                {"تدريب", "Training"},
                {"صيانة", "Maintenance"},
                {"حاسوب", "Computer"},
                {"شبكة", "Network"},
                {"برنامج", "Software"},
                {"تطبيقات الجوال", "Mobile Applications"},
                {"تطبيقات الويب", "Web Applications"},
                {"تطوير المواقع", "Website Development"},
                {"تصميم الجرافيك", "Graphic Design"},
                {"التسويق الرقمي", "Digital Marketing"},
                {"إدارة المشاريع", "Project Management"},
                {"تحليل البيانات", "Data Analysis"},
                {"الذكاء الاصطناعي", "Artificial Intelligence"},
                {"أمن المعلومات", "Information Security"},
                {"تطوير التطبيقات", "Application Development"}
            };
            
            var cleanText = arabicText.Trim();
            
            // Try exact match first
            if (transliterations.ContainsKey(cleanText))
                return transliterations[cleanText];
            
            // Try partial matches
            foreach (var pair in transliterations)
            {
                if (cleanText.Contains(pair.Key))
                    return cleanText.Replace(pair.Key, pair.Value);
            }
            
            // Check if contains Arabic characters
            foreach (char c in cleanText)
            {
                if (c >= 0x0600 && c <= 0x06FF)
                    return cleanText; // Return Arabic text as-is without "(Arabic)" suffix
            }

            return cleanText;
        }
        
        public void Dispose()
        {
            _titleFont?.Dispose();
            _headerFont?.Dispose();
            _normalFont?.Dispose();
            _boldFont?.Dispose();
            _smallFont?.Dispose();
        }
    }
}
