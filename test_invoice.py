#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مولد الفواتير
Test Invoice Generator
"""

from invoice_generator import InvoiceGenerator
import os

def test_invoice():
    """اختبار إنشاء فاتورة تجريبية"""
    
    # إنشاء مولد الفواتير
    generator = InvoiceGenerator()
    
    # بيانات تجريبية
    test_items = [
        {
            'name': 'خدمة تطوير موقع إلكتروني / Website Development',
            'quantity': 1,
            'price': 5000.00
        },
        {
            'name': 'تصميم شعار / Logo Design',
            'quantity': 2,
            'price': 500.00
        },
        {
            'name': 'استضافة سنوية / Annual Hosting',
            'quantity': 1,
            'price': 800.00
        }
    ]
    
    customer_name = "شركة التقنية المتقدمة / Advanced Tech Company"
    notes = "شكراً لتعاملكم معنا. يرجى السداد خلال 30 يوم.\nThank you for your business. Payment due within 30 days."
    
    try:
        # إنشاء الفاتورة
        filepath = generator.generate_invoice(test_items, customer_name, notes)
        print(f"✅ تم إنشاء الفاتورة التجريبية بنجاح!")
        print(f"✅ Test invoice created successfully!")
        print(f"📄 الملف: {filepath}")
        print(f"📄 File: {filepath}")
        
        # التحقق من وجود الملف
        if os.path.exists(filepath):
            print(f"✅ الملف موجود وحجمه: {os.path.getsize(filepath)} بايت")
            print(f"✅ File exists with size: {os.path.getsize(filepath)} bytes")
            return True
        else:
            print("❌ الملف غير موجود!")
            print("❌ File not found!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفاتورة: {str(e)}")
        print(f"❌ Error creating invoice: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار مولد الفواتير...")
    print("🧪 Testing Invoice Generator...")
    print("=" * 50)
    
    success = test_invoice()
    
    print("=" * 50)
    if success:
        print("🎉 الاختبار نجح! التطبيق جاهز للاستخدام")
        print("🎉 Test passed! Application is ready to use")
    else:
        print("💥 الاختبار فشل! يرجى مراجعة الأخطاء")
        print("💥 Test failed! Please check the errors")
