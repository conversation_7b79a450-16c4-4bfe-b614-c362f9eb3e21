using System;
using System.Drawing;
using System.Windows.Forms;
using HokTechPOS.Models;

namespace HokTechPOS.Forms
{
    public partial class CompanySettingsForm : Form
    {
        private CompanySettings _settings;
        
        // Controls
        private TextBox _companyNameTextBox = null!;
        private TextBox _companyNameArabicTextBox = null!;
        private TextBox _countryTextBox = null!;
        private TextBox _countryArabicTextBox = null!;
        private TextBox _phoneTextBox = null!;
        private TextBox _emailTextBox = null!;
        private TextBox _websiteTextBox = null!;
        private TextBox _thankYouMessageTextBox = null!;
        private TextBox _thankYouMessageArabicTextBox = null!;
        private Button _saveButton = null!;
        private Button _cancelButton = null!;
        private Button _resetButton = null!;
        
        public CompanySettingsForm()
        {
            _settings = CompanySettings.Load();
            InitializeComponent();
            LoadSettings();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Company Settings - إعدادات الشركة";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // Header
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(46, 134, 171),
                Padding = new Padding(20, 10, 20, 10)
            };
            
            var titleLabel = new Label
            {
                Text = "Company Settings\nإعدادات الشركة",
                Font = new Font("Arial", 14, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            headerPanel.Controls.Add(titleLabel);
            
            // Main panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30, 20, 30, 20),
                BackColor = Color.White,
                AutoScroll = true
            };
            
            int yPos = 20;
            int labelHeight = 25;
            int textBoxHeight = 30;
            int spacing = 15;
            
            // Company Name (English)
            var companyNameLabel = new Label
            {
                Text = "Company Name (English):",
                Location = new Point(20, yPos),
                Size = new Size(200, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(companyNameLabel);
            yPos += labelHeight + 5;
            
            _companyNameTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_companyNameTextBox);
            yPos += textBoxHeight + spacing;
            
            // Company Name (Arabic)
            var companyNameArabicLabel = new Label
            {
                Text = "Company Name (Arabic) - اسم الشركة:",
                Location = new Point(20, yPos),
                Size = new Size(250, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(companyNameArabicLabel);
            yPos += labelHeight + 5;
            
            _companyNameArabicTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11),
                RightToLeft = RightToLeft.Yes
            };
            mainPanel.Controls.Add(_companyNameArabicTextBox);
            yPos += textBoxHeight + spacing;
            
            // Country (English)
            var countryLabel = new Label
            {
                Text = "Country (English):",
                Location = new Point(20, yPos),
                Size = new Size(200, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(countryLabel);
            yPos += labelHeight + 5;
            
            _countryTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_countryTextBox);
            yPos += textBoxHeight + spacing;
            
            // Country (Arabic)
            var countryArabicLabel = new Label
            {
                Text = "Country (Arabic) - البلد:",
                Location = new Point(20, yPos),
                Size = new Size(200, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(countryArabicLabel);
            yPos += labelHeight + 5;
            
            _countryArabicTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11),
                RightToLeft = RightToLeft.Yes
            };
            mainPanel.Controls.Add(_countryArabicTextBox);
            yPos += textBoxHeight + spacing;
            
            // Phone
            var phoneLabel = new Label
            {
                Text = "Phone Number - رقم الهاتف:",
                Location = new Point(20, yPos),
                Size = new Size(200, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(phoneLabel);
            yPos += labelHeight + 5;
            
            _phoneTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_phoneTextBox);
            yPos += textBoxHeight + spacing;
            
            // Email
            var emailLabel = new Label
            {
                Text = "Email Address - البريد الإلكتروني:",
                Location = new Point(20, yPos),
                Size = new Size(250, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(emailLabel);
            yPos += labelHeight + 5;
            
            _emailTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_emailTextBox);
            yPos += textBoxHeight + spacing;
            
            // Website
            var websiteLabel = new Label
            {
                Text = "Website - الموقع الإلكتروني:",
                Location = new Point(20, yPos),
                Size = new Size(200, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(websiteLabel);
            yPos += labelHeight + 5;
            
            _websiteTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_websiteTextBox);
            yPos += textBoxHeight + spacing;
            
            // Thank You Message (English)
            var thankYouLabel = new Label
            {
                Text = "Thank You Message (English):",
                Location = new Point(20, yPos),
                Size = new Size(250, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(thankYouLabel);
            yPos += labelHeight + 5;
            
            _thankYouMessageTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11)
            };
            mainPanel.Controls.Add(_thankYouMessageTextBox);
            yPos += textBoxHeight + spacing;
            
            // Thank You Message (Arabic)
            var thankYouArabicLabel = new Label
            {
                Text = "Thank You Message (Arabic) - رسالة الشكر:",
                Location = new Point(20, yPos),
                Size = new Size(300, labelHeight),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(thankYouArabicLabel);
            yPos += labelHeight + 5;
            
            _thankYouMessageArabicTextBox = new TextBox
            {
                Location = new Point(20, yPos),
                Size = new Size(500, textBoxHeight),
                Font = new Font("Arial", 11),
                RightToLeft = RightToLeft.Yes
            };
            mainPanel.Controls.Add(_thankYouMessageArabicTextBox);
            yPos += textBoxHeight + spacing + 20;
            
            // Buttons panel
            var buttonsPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(250, 250, 250),
                Padding = new Padding(30, 15, 30, 15)
            };
            
            _saveButton = new Button
            {
                Text = "Save Settings\nحفظ الإعدادات",
                Location = new Point(30, 15),
                Size = new Size(150, 50),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _saveButton.FlatAppearance.BorderSize = 0;
            _saveButton.Click += SaveButton_Click;
            buttonsPanel.Controls.Add(_saveButton);
            
            _resetButton = new Button
            {
                Text = "Reset to Default\nإعادة تعيين",
                Location = new Point(200, 15),
                Size = new Size(150, 50),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _resetButton.FlatAppearance.BorderSize = 0;
            _resetButton.Click += ResetButton_Click;
            buttonsPanel.Controls.Add(_resetButton);
            
            _cancelButton = new Button
            {
                Text = "Cancel\nإلغاء",
                Location = new Point(370, 15),
                Size = new Size(150, 50),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                UseVisualStyleBackColor = false
            };
            _cancelButton.FlatAppearance.BorderSize = 0;
            _cancelButton.Click += CancelButton_Click;
            buttonsPanel.Controls.Add(_cancelButton);
            
            // Add panels to form
            this.Controls.Add(mainPanel);
            this.Controls.Add(buttonsPanel);
            this.Controls.Add(headerPanel);
            
            this.ResumeLayout(false);
        }
        
        private void LoadSettings()
        {
            _companyNameTextBox.Text = _settings.CompanyName;
            _companyNameArabicTextBox.Text = _settings.CompanyNameArabic;
            _countryTextBox.Text = _settings.Country;
            _countryArabicTextBox.Text = _settings.CountryArabic;
            _phoneTextBox.Text = _settings.Phone;
            _emailTextBox.Text = _settings.Email;
            _websiteTextBox.Text = _settings.Website;
            _thankYouMessageTextBox.Text = _settings.ThankYouMessage;
            _thankYouMessageArabicTextBox.Text = _settings.ThankYouMessageArabic;
        }
        
        private void SaveSettings()
        {
            _settings.CompanyName = _companyNameTextBox.Text.Trim();
            _settings.CompanyNameArabic = _companyNameArabicTextBox.Text.Trim();
            _settings.Country = _countryTextBox.Text.Trim();
            _settings.CountryArabic = _countryArabicTextBox.Text.Trim();
            _settings.Phone = _phoneTextBox.Text.Trim();
            _settings.Email = _emailTextBox.Text.Trim();
            _settings.Website = _websiteTextBox.Text.Trim();
            _settings.ThankYouMessage = _thankYouMessageTextBox.Text.Trim();
            _settings.ThankYouMessageArabic = _thankYouMessageArabicTextBox.Text.Trim();
        }
        
        private void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                SaveSettings();
                _settings.Save();
                
                MessageBox.Show("تم حفظ الإعدادات بنجاح!\nSettings saved successfully!", 
                    "نجح الحفظ - Success", 
                    MessageBoxButtons.OK, 
                    MessageBoxIcon.Information);
                
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الإعدادات:\nError saving settings:\n{ex.Message}", 
                    "خطأ - Error", 
                    MessageBoxButtons.OK, 
                    MessageBoxIcon.Error);
            }
        }
        
        private void ResetButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟\nDo you want to reset all settings to default values?",
                "تأكيد إعادة التعيين - Confirm Reset",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                _settings = new CompanySettings();
                LoadSettings();
            }
        }
        
        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
