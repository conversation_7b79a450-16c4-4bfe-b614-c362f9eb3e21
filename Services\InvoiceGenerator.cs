using System;
using System.IO;
using System.Drawing;
using System.Text;
using System.Collections.Generic;
using iTextSharp.text;
using iTextSharp.text.pdf;
using HokTechPOS.Models;
using System.Diagnostics;

namespace HokTechPOS.Services
{
    public class InvoiceGenerator
    {
        private readonly string _invoicesFolder = "invoices";
        private readonly string _logoPath = "HOKTECH Logo Design with Circuit Elements.png";
        
        public InvoiceGenerator()
        {
            EnsureInvoicesFolder();
        }

        // Helper method to detect Arabic characters
        private bool ContainsArabic(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            foreach (char c in text)
            {
                // Arabic Unicode range: U+0600 to U+06FF
                if (c >= 0x0600 && c <= 0x06FF)
                    return true;
            }
            return false;
        }

        // Helper method to transliterate Arabic text for PDF display
        private string TransliterateArabicText(string arabicText)
        {
            if (string.IsNullOrEmpty(arabicText))
                return "Product/Service";

            // Common Arabic words and their transliterations
            var transliterations = new Dictionary<string, string>
            {
                {"تطبيقات", "Applications"},
                {"تطبيق", "Application"},
                {"تطوير", "Development"},
                {"موقع", "Website"},
                {"إلكتروني", "Electronic"},
                {"تصميم", "Design"},
                {"شعار", "Logo"},
                {"احترافي", "Professional"},
                {"دعم", "Support"},
                {"تقني", "Technical"},
                {"شامل", "Comprehensive"},
                {"خدمة", "Service"},
                {"خدمات", "Services"},
                {"برمجة", "Programming"},
                {"نظام", "System"},
                {"إدارة", "Management"},
                {"قاعدة", "Database"},
                {"بيانات", "Data"},
                {"أمان", "Security"},
                {"حماية", "Protection"},
                {"استشارة", "Consultation"},
                {"تدريب", "Training"},
                {"صيانة", "Maintenance"},
                {"حاسوب", "Computer"},
                {"شبكة", "Network"},
                {"برنامج", "Software"},
                {"تطبيقات الجوال", "Mobile Applications"},
                {"تطبيقات الويب", "Web Applications"},
                {"تطوير المواقع", "Website Development"},
                {"تصميم الجرافيك", "Graphic Design"},
                {"التسويق الرقمي", "Digital Marketing"},
                {"إدارة المشاريع", "Project Management"},
                {"تحليل البيانات", "Data Analysis"},
                {"الذكاء الاصطناعي", "Artificial Intelligence"},
                {"أمن المعلومات", "Information Security"},
                {"تطوير التطبيقات", "Application Development"}
            };

            // Try to find exact matches first
            if (transliterations.ContainsKey(arabicText.Trim()))
            {
                return transliterations[arabicText.Trim()];
            }

            // Try to find partial matches
            foreach (var pair in transliterations)
            {
                if (arabicText.Contains(pair.Key))
                {
                    return arabicText.Replace(pair.Key, pair.Value);
                }
            }

            // If no match found, return the original text with a note
            return $"{arabicText} (Arabic)";
        }
        
        private void EnsureInvoicesFolder()
        {
            if (!Directory.Exists(_invoicesFolder))
            {
                Directory.CreateDirectory(_invoicesFolder);
            }
        }
        
        public int GetNextInvoiceNumber()
        {
            var files = Directory.GetFiles(_invoicesFolder, "Invoice-*.pdf");
            int maxNumber = 0;
            
            foreach (var file in files)
            {
                var fileName = Path.GetFileNameWithoutExtension(file);
                if (fileName.StartsWith("Invoice-"))
                {
                    var numberPart = fileName.Substring(8);
                    if (int.TryParse(numberPart, out int number))
                    {
                        maxNumber = Math.Max(maxNumber, number);
                    }
                }
            }
            
            return maxNumber + 1;
        }
        
        public string GenerateInvoice(Invoice invoice)
        {
            var fileName = $"Invoice-{invoice.InvoiceNumber:D4}.pdf";
            var filePath = Path.Combine(_invoicesFolder, fileName);

            using (var document = new Document(PageSize.A4, 50, 50, 50, 50))
            {
                using (var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create)))
                {
                    // Set encoding for Arabic support
                    writer.SetEncryption(null, null, PdfWriter.ALLOW_PRINTING, PdfWriter.STANDARD_ENCRYPTION_128);
                    document.Open();
                    
                    // Add content
                    AddHeader(document);
                    AddInvoiceInfo(document, invoice);
                    AddCustomerInfo(document, invoice);
                    AddProductsTable(document, invoice);
                    AddTotals(document, invoice);
                    AddNotes(document, invoice);
                    AddFooter(document);
                    
                    document.Close();
                }
            }
            
            return filePath;
        }
        
        private void AddHeader(Document document)
        {
            // Company logo
            if (File.Exists(_logoPath))
            {
                try
                {
                    var logo = iTextSharp.text.Image.GetInstance(_logoPath);
                    logo.ScaleToFit(120f, 60f);
                    logo.Alignment = Element.ALIGN_CENTER;
                    document.Add(logo);
                }
                catch
                {
                    // If logo fails, continue without it
                }
            }
            
            // Company name
            var companyFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 24, BaseColor.BLUE);
            var companyName = new Paragraph("HokTech", companyFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 10f
            };
            document.Add(companyName);
            
            // Invoice title
            var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.DARK_GRAY);
            var title = new Paragraph("INVOICE", titleFont)
            {
                Alignment = Element.ALIGN_CENTER,
                SpacingAfter = 20f
            };
            document.Add(title);
        }
        
        private void AddInvoiceInfo(Document document, Invoice invoice)
        {
            var table = new PdfPTable(2) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 1f, 1f });
            
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 10);
            
            table.AddCell(new PdfPCell(new Phrase($"Invoice Number: {invoice.InvoiceNumber:D4}", font)) { Border = 0 });
            table.AddCell(new PdfPCell(new Phrase($"Date: {invoice.Date:yyyy-MM-dd}", font)) { Border = 0 });
            table.AddCell(new PdfPCell(new Phrase($"Time: {invoice.Date:HH:mm}", font)) { Border = 0 });
            table.AddCell(new PdfPCell(new Phrase("", font)) { Border = 0 });
            
            table.SpacingAfter = 20f;
            document.Add(table);
        }
        
        private void AddCustomerInfo(Document document, Invoice invoice)
        {
            if (!string.IsNullOrWhiteSpace(invoice.CustomerName))
            {
                var font = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                var customer = new Paragraph($"Customer: {invoice.CustomerName}", font)
                {
                    SpacingAfter = 15f
                };
                document.Add(customer);
            }
        }
        
        private void AddProductsTable(Document document, Invoice invoice)
        {
            var table = new PdfPTable(4) { WidthPercentage = 100 };
            table.SetWidths(new float[] { 3f, 1f, 1.5f, 1.5f });

            // Header
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12, BaseColor.WHITE);
            var headerColor = new BaseColor(46, 134, 171); // HokTech blue
            
            var headers = new[] { "Product/Service", "Quantity", "Unit Price (EGP)", "Total (EGP)" };
            foreach (var header in headers)
            {
                var cell = new PdfPCell(new Phrase(header, headerFont))
                {
                    BackgroundColor = headerColor,
                    HorizontalAlignment = Element.ALIGN_CENTER,
                    Padding = 8f
                };
                table.AddCell(cell);
            }
            
            // Products
            var cellFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);
            foreach (var product in invoice.Products)
            {
                // Handle Arabic text properly - check for null, empty, or whitespace
                var productName = string.IsNullOrWhiteSpace(product.Name?.Trim()) ?
                    "Product/Service" :
                    product.Name.Trim();

                // Debug: Log the product name to see what we're getting
                System.Diagnostics.Debug.WriteLine($"Product Name: '{product.Name}' -> '{productName}'");

                // For Arabic text, we need to ensure proper encoding
                // If the text contains Arabic characters, we'll use a transliteration approach
                if (ContainsArabic(productName))
                {
                    // Convert Arabic text to a readable format for PDF
                    var transliteratedText = TransliterateArabicText(productName);
                    table.AddCell(new PdfPCell(new Phrase(transliteratedText, cellFont)) { Padding = 5f });
                }
                else
                {
                    table.AddCell(new PdfPCell(new Phrase(productName, cellFont)) { Padding = 5f });
                }
                table.AddCell(new PdfPCell(new Phrase(product.Quantity.ToString(), cellFont)) 
                { 
                    HorizontalAlignment = Element.ALIGN_CENTER, 
                    Padding = 5f 
                });
                table.AddCell(new PdfPCell(new Phrase($"{product.Price:F2}", cellFont)) 
                { 
                    HorizontalAlignment = Element.ALIGN_RIGHT, 
                    Padding = 5f 
                });
                table.AddCell(new PdfPCell(new Phrase($"{product.Total:F2}", cellFont)) 
                { 
                    HorizontalAlignment = Element.ALIGN_RIGHT, 
                    Padding = 5f 
                });
            }
            
            table.SpacingAfter = 20f;
            document.Add(table);
        }
        
        private void AddTotals(Document document, Invoice invoice)
        {
            var table = new PdfPTable(2) { WidthPercentage = 60, HorizontalAlignment = Element.ALIGN_RIGHT };
            table.SetWidths(new float[] { 1f, 1f });
            
            var labelFont = FontFactory.GetFont(FontFactory.HELVETICA, 12);
            var valueFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
            var totalFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.WHITE);
            
            // Subtotal
            table.AddCell(new PdfPCell(new Phrase("Subtotal:", labelFont)) 
            { 
                Border = 0, 
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 5f 
            });
            table.AddCell(new PdfPCell(new Phrase($"{invoice.Subtotal:F2} EGP", valueFont))
            { 
                Border = 0, 
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 5f 
            });
            
            // VAT
            table.AddCell(new PdfPCell(new Phrase($"VAT ({invoice.TaxRate:P0}):", labelFont)) 
            { 
                Border = 0, 
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 5f 
            });
            table.AddCell(new PdfPCell(new Phrase($"{invoice.TaxAmount:F2} EGP", valueFont))
            { 
                Border = 0, 
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 5f 
            });
            
            // Total
            var totalColor = new BaseColor(46, 134, 171);
            table.AddCell(new PdfPCell(new Phrase("Total:", totalFont)) 
            { 
                BackgroundColor = totalColor,
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 8f 
            });
            table.AddCell(new PdfPCell(new Phrase($"{invoice.Total:F2} EGP", totalFont))
            { 
                BackgroundColor = totalColor,
                HorizontalAlignment = Element.ALIGN_RIGHT, 
                Padding = 8f 
            });
            
            table.SpacingAfter = 30f;
            document.Add(table);
        }
        
        private void AddNotes(Document document, Invoice invoice)
        {
            if (!string.IsNullOrWhiteSpace(invoice.Notes))
            {
                var font = FontFactory.GetFont(FontFactory.HELVETICA, 10);
                var notes = new Paragraph($"Notes:\n{invoice.Notes}", font)
                {
                    SpacingAfter = 20f
                };
                document.Add(notes);
            }
        }
        
        private void AddFooter(Document document)
        {
            var font = FontFactory.GetFont(FontFactory.HELVETICA, 9, BaseColor.GRAY);
            var footer = new Paragraph(
                "HokTech\n" +
                "Saudi Arabia\n" +
                "Phone: +966 XX XXX XXXX | Email: <EMAIL>\n" +
                "Website: www.hoktech.com\n\n" +
                "Thank you for your business!",
                font)
            {
                Alignment = Element.ALIGN_CENTER
            };
            document.Add(footer);
        }
        
        public void OpenInvoice(string filePath)
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"Cannot open invoice: {ex.Message}");
            }
        }
    }
}
