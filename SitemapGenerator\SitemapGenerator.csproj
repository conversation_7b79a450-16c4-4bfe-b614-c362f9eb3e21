<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <AssemblyTitle>SEO Sitemap Generator</AssemblyTitle>
    <AssemblyDescription>Professional SEO Sitemap Generation Tool with Google Compliance</AssemblyDescription>
    <AssemblyCompany>HokTech</AssemblyCompany>
    <AssemblyProduct>SEO Sitemap Generator</AssemblyProduct>
    <AssemblyCopyright>Copyright © HokTech 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.54" />
    <PackageReference Include="System.Drawing.Common" Version="7.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>



</Project>
