# 🚀 SEO Sitemap Generator - HokTech
# مولد خرائط المواقع للسيو - هوك تك

## 🎯 نظرة عامة / Overview

**SEO Sitemap Generator** هو أداة شاملة ومتقدمة لإنشاء خرائط المواقع XML المتوافقة مع معايير Google SEO. تم تطويرها باستخدام C# و Windows Forms لتوفير أفضل أداء وسهولة استخدام.

**SEO Sitemap Generator** is a comprehensive and advanced tool for creating XML sitemaps that comply with Google SEO standards. Developed using C# and Windows Forms to provide the best performance and ease of use.

---

## ✨ الميزات الرئيسية / Key Features

### 🔍 **اكتشاف تلقائي للروابط / Automatic URL Discovery**
- زحف ذكي للمواقع الإلكترونية
- اكتشاف تلقائي لجميع الصفحات والروابط
- دعم المواقع متعددة اللغات
- تحليل SEO تلقائي للصفحات

### 📋 **إدارة متقدمة للروابط / Advanced URL Management**
- إضافة وتعديل الروابط يدوياً
- تصفية وبحث متقدم
- تحديد أولوية الصفحات
- إعداد تكرار التحديث

### 🎨 **أنواع خرائط المواقع المتعددة / Multiple Sitemap Types**
- **خريطة الموقع الأساسية** - للصفحات العادية
- **خريطة الصور** - لتحسين SEO للصور
- **خريطة الفيديو** - للمحتوى المرئي
- **خريطة الأخبار** - للمواقع الإخبارية

### 🌐 **دعم متعدد اللغات / Multi-language Support**
- دعم كامل للغة العربية والإنجليزية
- إعدادات hreflang تلقائية
- دعم أكثر من 10 لغات

### ⚙️ **إعدادات متقدمة / Advanced Settings**
- تخصيص كامل لمعايير الزحف
- إعدادات SEO متقدمة
- ضغط الملفات تلقائياً
- إنشاء robots.txt تلقائي

---

## 🚀 التثبيت والتشغيل / Installation & Running

### المتطلبات / Requirements:
- **Windows 10** أو أحدث
- **.NET 6.0** أو أحدث
- **اتصال بالإنترنت** للزحف

### طريقة التشغيل السريع / Quick Start:

#### 1️⃣ تشغيل مباشر / Direct Run:
```bash
# انقر مرتين على الملف
run_sitemap_generator.bat
```

#### 2️⃣ سطر الأوامر / Command Line:
```bash
# بناء وتشغيل المشروع
dotnet run --project SitemapGenerator.csproj

# أو بناء منفصل
dotnet build SitemapGenerator.csproj --configuration Release
dotnet run --project SitemapGenerator.csproj --configuration Release
```

---

## 📖 دليل الاستخدام / User Guide

### 🔍 **تبويب الزحف / Crawl Tab**
1. أدخل رابط الموقع الإلكتروني
2. اضغط "Start Crawl" لبدء الزحف
3. راقب التقدم في نافذة السجل
4. انتظر حتى اكتمال الزحف

### 📋 **تبويب إدارة الروابط / URLs Management Tab**
- **عرض جميع الروابط** المكتشفة
- **تصفية الروابط** باستخدام مربع البحث
- **إضافة روابط جديدة** يدوياً
- **تعديل خصائص الروابط** (الأولوية، التكرار، إلخ)
- **حذف الروابط** غير المرغوبة

### ⚙️ **تبويب الإعدادات / Settings Tab**
- **إعدادات الموقع الأساسية**
- **معايير الزحف المتقدمة**
- **خيارات SEO**
- **إعدادات الإخراج**

### 🎯 **تبويب إنشاء خريطة الموقع / Generate Sitemap Tab**
- **إنشاء جميع ملفات خريطة الموقع**
- **مراقبة عملية الإنشاء**
- **عرض الملفات المُنشأة**
- **فتح مجلد الإخراج**

---

## 📁 هيكل الملفات / File Structure

```
SitemapGenerator/
├── 📄 Program.cs                    # نقطة البداية
├── 📄 MainForm.cs                   # النموذج الرئيسي
├── 📄 UrlEditDialog.cs              # نافذة تعديل الروابط
├── 📁 Models/
│   ├── SitemapUrl.cs                # نموذج الرابط
│   └── SitemapSettings.cs           # نموذج الإعدادات
├── 📁 Services/
│   ├── WebCrawler.cs                # خدمة الزحف
│   └── SitemapBuilder.cs            # خدمة بناء خريطة الموقع
├── 📄 SitemapGenerator.csproj       # ملف المشروع
├── 📄 run_sitemap_generator.bat     # ملف التشغيل السريع
└── 📁 sitemaps/                     # مجلد الإخراج (ينشأ تلقائياً)
```

---

## 🎨 أنواع الملفات المُنشأة / Generated File Types

### 📄 **ملفات XML الأساسية / Basic XML Files**
- `sitemap.xml` - خريطة الموقع الرئيسية
- `sitemap-index.xml` - فهرس خرائط المواقع (للمواقع الكبيرة)
- `robots.txt` - ملف توجيهات محركات البحث

### 🖼️ **ملفات متخصصة / Specialized Files**
- `sitemap-images.xml` - خريطة الصور
- `sitemap-videos.xml` - خريطة الفيديو
- `sitemap-news.xml` - خريطة الأخبار

### 📦 **ملفات مضغوطة / Compressed Files**
- `*.xml.gz` - ملفات مضغوطة (اختيارية)

---

## 🔧 الإعدادات المتقدمة / Advanced Configuration

### ⚡ **إعدادات الزحف / Crawl Settings**
```json
{
  "MaxDepth": 5,              // أقصى عمق للزحف
  "MaxPages": 10000,          // أقصى عدد صفحات
  "DelayBetweenRequests": 1000, // تأخير بين الطلبات (مللي ثانية)
  "TimeoutSeconds": 30,       // مهلة الطلب
  "RespectRobotsTxt": true    // احترام ملف robots.txt
}
```

### 🎯 **إعدادات SEO / SEO Settings**
```json
{
  "ValidateUrls": true,           // التحقق من صحة الروابط
  "CheckDuplicateContent": true,  // فحص المحتوى المكرر
  "AnalyzeSeoElements": true,     // تحليل عناصر SEO
  "DetectCanonicalUrls": true,    // اكتشاف الروابط الأساسية
  "IncludeHreflang": true         // تضمين hreflang
}
```

---

## 🌟 مميزات متقدمة / Advanced Features

### 🔍 **تحليل SEO تلقائي / Automatic SEO Analysis**
- استخراج العناوين والأوصاف
- تحليل الكلمات المفتاحية
- اكتشاف الصور والفيديو
- فحص الروابط المكسورة

### 🌐 **دعم المواقع متعددة اللغات / Multi-language Website Support**
- إعدادات hreflang تلقائية
- دعم اتجاه النص (RTL/LTR)
- ترجمة تلقائية للمصطلحات

### 📊 **تقارير مفصلة / Detailed Reports**
- إحصائيات شاملة للزحف
- تقارير أخطاء مفصلة
- تحليل أداء SEO

---

## 🛠️ استكشاف الأخطاء / Troubleshooting

### ❌ **مشاكل شائعة / Common Issues**

#### 🔗 **خطأ في الاتصال / Connection Error**
```
الحل: تأكد من صحة رابط الموقع واتصال الإنترنت
Solution: Verify website URL and internet connection
```

#### 🚫 **رفض الوصول / Access Denied**
```
الحل: تحقق من ملف robots.txt أو إعدادات الخادم
Solution: Check robots.txt file or server settings
```

#### 💾 **خطأ في حفظ الملفات / File Save Error**
```
الحل: تأكد من صلاحيات الكتابة في مجلد الإخراج
Solution: Ensure write permissions for output directory
```

---

## 📞 الدعم والمساعدة / Support & Help

### 🆘 **الحصول على المساعدة / Getting Help**
- **دليل المستخدم المدمج** - من قائمة Help
- **رسائل الخطأ التفصيلية** - في نوافذ السجل
- **التحقق من الإعدادات** - في تبويب Settings

### 🔄 **التحديثات / Updates**
- تحقق من التحديثات دورياً
- احتفظ بنسخة احتياطية من الإعدادات
- راجع ملاحظات الإصدار

---

## 🎯 أمثلة عملية / Practical Examples

### 🌐 **مثال: موقع تجاري / Example: Business Website**
```
1. أدخل: https://example.com
2. اضبط العمق: 3 مستويات
3. فعّل: خريطة الصور
4. اضبط الأولوية: الصفحة الرئيسية = 1.0
5. أنشئ خريطة الموقع
```

### 📰 **مثال: موقع إخباري / Example: News Website**
```
1. أدخل: https://news-site.com
2. فعّل: خريطة الأخبار
3. اضبط التكرار: يومي
4. حدد اللغات: العربية والإنجليزية
5. أنشئ خريطة الموقع
```

---

## 🏆 **نظام HokTech SEO Sitemap Generator - الحل الشامل!**

### ✅ **مزايا فريدة / Unique Advantages**
- **واجهة عربية كاملة** - دعم مثالي للغة العربية
- **أداء فائق** - تطبيق Windows أصلي
- **معايير Google كاملة** - متوافق 100% مع Google
- **سهولة الاستخدام** - واجهة بديهية ومفهومة
- **مجاني تماماً** - بدون قيود أو رسوم

---

## 🚀 **ابدأ الآن! / Start Now!**

```bash
# تشغيل فوري
run_sitemap_generator.bat

# أو
dotnet run --project SitemapGenerator.csproj
```

**🎯 مولد خرائط المواقع للسيو - الأداة الأكثر تقدماً وشمولية!**

---

*تم التطوير بواسطة Augment Agent لشركة HokTech*  
*💎 نسخة C# - أداء مثالي، ميزات شاملة!*
