using System;
using System.Drawing;
using System.Windows.Forms;
using SitemapGenerator.Models;

namespace SitemapGenerator
{
    /// <summary>
    /// Dialog for adding/editing sitemap URLs
    /// </summary>
    public partial class UrlEditDialog : Form
    {
        public SitemapUrl? SitemapUrl { get; private set; }

        private TextBox txtUrl = null!;
        private TextBox txtTitle = null!;
        private TextBox txtDescription = null!;
        private TextBox txtKeywords = null!;
        private ComboBox cmbLanguage = null!;
        private ComboBox cmbChangeFrequency = null!;
        private NumericUpDown nudPriority = null!;
        private DateTimePicker dtpLastModified = null!;
        private CheckBox chkIncluded = null!;
        private ComboBox cmbUrlType = null!;
        private Button btnOK = null!;
        private Button btnCancel = null!;

        public UrlEditDialog(SitemapUrl? existingUrl = null)
        {
            InitializeComponent();
            
            if (existingUrl != null)
            {
                SitemapUrl = existingUrl;
                LoadUrlData();
                this.Text = "Edit URL";
            }
            else
            {
                SitemapUrl = new SitemapUrl();
                this.Text = "Add URL";
            }
        }

        private void InitializeComponent()
        {
            this.Size = new Size(500, 450);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Font = new Font("Segoe UI", 9F);

            var y = 20;
            const int labelWidth = 120;
            const int controlWidth = 300;
            const int spacing = 35;

            // URL
            var lblUrl = new Label
            {
                Text = "URL:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtUrl = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 23),
                Font = new Font("Segoe UI", 9F)
            };
            y += spacing;

            // Title
            var lblTitle = new Label
            {
                Text = "Title:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtTitle = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 23)
            };
            y += spacing;

            // Description
            var lblDescription = new Label
            {
                Text = "Description:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtDescription = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 60),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            y += 70;

            // Keywords
            var lblKeywords = new Label
            {
                Text = "Keywords:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            txtKeywords = new TextBox
            {
                Location = new Point(150, y),
                Size = new Size(controlWidth, 23)
            };
            y += spacing;

            // Language
            var lblLanguage = new Label
            {
                Text = "Language:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            cmbLanguage = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            cmbLanguage.Items.AddRange(new[] { "en", "ar", "es", "fr", "de", "it", "pt", "ru", "zh", "ja" });
            cmbLanguage.SelectedItem = "en";
            y += spacing;

            // Change Frequency
            var lblChangeFreq = new Label
            {
                Text = "Change Frequency:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            cmbChangeFrequency = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                DataSource = Enum.GetValues(typeof(ChangeFrequency))
            };
            cmbChangeFrequency.SelectedItem = ChangeFrequency.Weekly;
            y += spacing;

            // Priority
            var lblPriority = new Label
            {
                Text = "Priority (0.0-1.0):",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            nudPriority = new NumericUpDown
            {
                Location = new Point(150, y),
                Size = new Size(100, 23),
                Minimum = 0.0m,
                Maximum = 1.0m,
                DecimalPlaces = 1,
                Increment = 0.1m,
                Value = 0.5m
            };
            y += spacing;

            // Last Modified
            var lblLastModified = new Label
            {
                Text = "Last Modified:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            dtpLastModified = new DateTimePicker
            {
                Location = new Point(150, y),
                Size = new Size(200, 23),
                Format = DateTimePickerFormat.Short,
                Value = DateTime.Now
            };
            y += spacing;

            // URL Type
            var lblUrlType = new Label
            {
                Text = "URL Type:",
                Location = new Point(20, y),
                Size = new Size(labelWidth, 20)
            };
            cmbUrlType = new ComboBox
            {
                Location = new Point(150, y),
                Size = new Size(150, 23),
                DropDownStyle = ComboBoxStyle.DropDownList,
                DataSource = Enum.GetValues(typeof(UrlType))
            };
            cmbUrlType.SelectedItem = UrlType.Page;
            y += spacing;

            // Include checkbox
            chkIncluded = new CheckBox
            {
                Text = "Include in sitemap",
                Location = new Point(150, y),
                Size = new Size(150, 20),
                Checked = true
            };
            y += spacing + 10;

            // Buttons
            btnOK = new Button
            {
                Text = "OK",
                Location = new Point(280, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(370, y),
                Size = new Size(80, 30),
                BackColor = Color.FromArgb(162, 59, 114),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };

            this.Controls.AddRange(new Control[]
            {
                lblUrl, txtUrl,
                lblTitle, txtTitle,
                lblDescription, txtDescription,
                lblKeywords, txtKeywords,
                lblLanguage, cmbLanguage,
                lblChangeFreq, cmbChangeFrequency,
                lblPriority, nudPriority,
                lblLastModified, dtpLastModified,
                lblUrlType, cmbUrlType,
                chkIncluded,
                btnOK, btnCancel
            });

            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private void LoadUrlData()
        {
            if (SitemapUrl == null) return;

            txtUrl.Text = SitemapUrl.Url;
            txtTitle.Text = SitemapUrl.Title;
            txtDescription.Text = SitemapUrl.Description;
            txtKeywords.Text = SitemapUrl.Keywords;
            cmbLanguage.SelectedItem = SitemapUrl.Language;
            cmbChangeFrequency.SelectedItem = SitemapUrl.ChangeFrequency;
            nudPriority.Value = (decimal)SitemapUrl.Priority;
            dtpLastModified.Value = SitemapUrl.LastModified;
            cmbUrlType.SelectedItem = SitemapUrl.UrlType;
            chkIncluded.Checked = SitemapUrl.IsIncluded;
        }

        private void BtnOK_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUrl.Text))
            {
                MessageBox.Show("Please enter a valid URL.", "Validation Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUrl.Focus();
                return;
            }

            if (!Uri.TryCreate(txtUrl.Text.Trim(), UriKind.Absolute, out _))
            {
                MessageBox.Show("Please enter a valid URL format (e.g., https://example.com/page).", 
                    "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtUrl.Focus();
                return;
            }

            // Save data to SitemapUrl object
            if (SitemapUrl == null)
                SitemapUrl = new SitemapUrl();

            SitemapUrl.Url = txtUrl.Text.Trim();
            SitemapUrl.Title = txtTitle.Text.Trim();
            SitemapUrl.Description = txtDescription.Text.Trim();
            SitemapUrl.Keywords = txtKeywords.Text.Trim();
            SitemapUrl.Language = cmbLanguage.SelectedItem?.ToString() ?? "en";
            SitemapUrl.ChangeFrequency = (ChangeFrequency)(cmbChangeFrequency.SelectedItem ?? ChangeFrequency.Weekly);
            SitemapUrl.Priority = (double)nudPriority.Value;
            SitemapUrl.LastModified = dtpLastModified.Value;
            SitemapUrl.UrlType = (UrlType)(cmbUrlType.SelectedItem ?? UrlType.Page);
            SitemapUrl.IsIncluded = chkIncluded.Checked;

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
