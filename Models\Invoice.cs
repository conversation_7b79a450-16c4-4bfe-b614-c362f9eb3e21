using System;
using System.Collections.Generic;
using System.Linq;

namespace HokTechPOS.Models
{
    public class Invoice
    {
        public int InvoiceNumber { get; set; }
        public DateTime Date { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public List<Product> Products { get; set; } = new List<Product>();
        
        public decimal Subtotal => Products.Sum(p => p.Total);
        public decimal TaxRate { get; set; } = 0.15m; // 15% VAT
        public decimal TaxAmount => Subtotal * TaxRate;
        public decimal Total => Subtotal + TaxAmount;
        
        public Invoice()
        {
            Date = DateTime.Now;
        }
        
        public Invoice(int invoiceNumber, string customerName = "", string notes = "")
        {
            InvoiceNumber = invoiceNumber;
            Date = DateTime.Now;
            CustomerName = customerName;
            Notes = notes;
        }
        
        public void AddProduct(Product product)
        {
            Products.Add(product);
        }
        
        public void RemoveProduct(Product product)
        {
            Products.Remove(product);
        }
        
        public void ClearProducts()
        {
            Products.Clear();
        }
    }
}
