using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SitemapGenerator.Models;
using SitemapGenerator.Services;

namespace SitemapGenerator
{
    /// <summary>
    /// Main form for the SEO Sitemap Generator application
    /// </summary>
    public partial class MainForm : Form
    {
        private SitemapSettings _settings = null!;
        private WebCrawler? _crawler;
        private List<SitemapUrl> _urls = null!;
        private BindingSource _urlsBindingSource = null!;

        // UI Controls
        private TabControl tabControl = null!;
        private TabPage tabCrawl = null!, tabUrls = null!, tabSettings = null!, tabGenerate = null!;

        // Crawl tab controls
        private TextBox txtBaseUrl = null!;
        private Button btnStartCrawl = null!, btnStopCrawl = null!;
        private ProgressBar progressCrawl = null!;
        private Label lblCrawlStatus = null!;
        private RichTextBox txtCrawlLog = null!;

        // URLs tab controls
        private DataGridView dgvUrls = null!;
        private Button btnAddUrl = null!, btnEditUrl = null!, btnDeleteUrl = null!, btnClearUrls = null!;
        private TextBox txtFilterUrls = null!;

        // Settings tab controls
        private PropertyGrid pgSettings = null!;
        private Button btnSaveSettings = null!, btnResetSettings = null!;

        // Generate tab controls
        private Button btnGenerateSitemap = null!;
        private RichTextBox txtGenerateLog = null!;
        private ProgressBar progressGenerate = null!;
        private Label lblGenerateStatus = null!;

        public MainForm()
        {
            InitializeComponent();
            LoadSettings();
            InitializeData();
        }

        private void InitializeComponent()
        {
            this.Text = "SEO Sitemap Generator - HokTech";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = SystemIcons.Application;

            // Create tab control
            tabControl = new TabControl
            {
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9F)
            };

            // Create tabs
            CreateCrawlTab();
            CreateUrlsTab();
            CreateSettingsTab();
            CreateGenerateTab();

            this.Controls.Add(tabControl);

            // Create menu
            CreateMenuStrip();
        }

        private void CreateMenuStrip()
        {
            var menuStrip = new MenuStrip();
            
            // File menu
            var fileMenu = new ToolStripMenuItem("File");
            fileMenu.DropDownItems.Add("New Project", null, (s, e) => NewProject());
            fileMenu.DropDownItems.Add("Open Project", null, (s, e) => OpenProject());
            fileMenu.DropDownItems.Add("Save Project", null, (s, e) => SaveProject());
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("Exit", null, (s, e) => this.Close());
            
            // Tools menu
            var toolsMenu = new ToolStripMenuItem("Tools");
            toolsMenu.DropDownItems.Add("Validate Sitemap", null, (s, e) => ValidateSitemap());
            toolsMenu.DropDownItems.Add("Open Output Folder", null, (s, e) => OpenOutputFolder());
            toolsMenu.DropDownItems.Add(new ToolStripSeparator());
            toolsMenu.DropDownItems.Add("Settings", null, (s, e) => tabControl.SelectedTab = tabSettings);
            
            // Help menu
            var helpMenu = new ToolStripMenuItem("Help");
            helpMenu.DropDownItems.Add("About", null, (s, e) => ShowAbout());
            helpMenu.DropDownItems.Add("User Guide", null, (s, e) => ShowUserGuide());
            
            menuStrip.Items.AddRange(new ToolStripItem[] { fileMenu, toolsMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateCrawlTab()
        {
            tabCrawl = new TabPage("Website Crawl");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            // URL input
            var lblUrl = new Label { Text = "Website URL:", Location = new Point(0, 10), AutoSize = true };
            txtBaseUrl = new TextBox 
            { 
                Location = new Point(0, 35), 
                Width = 400, 
                Text = _settings.BaseUrl,
                Font = new Font("Segoe UI", 10F)
            };
            
            // Crawl buttons
            btnStartCrawl = new Button 
            { 
                Text = "Start Crawl", 
                Location = new Point(410, 33), 
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnStartCrawl.Click += BtnStartCrawl_Click;
            
            btnStopCrawl = new Button 
            { 
                Text = "Stop Crawl", 
                Location = new Point(520, 33), 
                Size = new Size(100, 25),
                BackColor = Color.FromArgb(162, 59, 114),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Enabled = false
            };
            btnStopCrawl.Click += BtnStopCrawl_Click;
            
            // Progress and status
            progressCrawl = new ProgressBar 
            { 
                Location = new Point(0, 70), 
                Width = 620, 
                Height = 20 
            };
            
            lblCrawlStatus = new Label 
            { 
                Text = "Ready to crawl", 
                Location = new Point(0, 100), 
                AutoSize = true,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            
            // Log
            var lblLog = new Label { Text = "Crawl Log:", Location = new Point(0, 130), AutoSize = true };
            txtCrawlLog = new RichTextBox 
            { 
                Location = new Point(0, 155), 
                Size = new Size(950, 400),
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.FromArgb(248, 248, 248)
            };
            
            panel.Controls.AddRange(new Control[] 
            { 
                lblUrl, txtBaseUrl, btnStartCrawl, btnStopCrawl, 
                progressCrawl, lblCrawlStatus, lblLog, txtCrawlLog 
            });
            
            tabCrawl.Controls.Add(panel);
            tabControl.TabPages.Add(tabCrawl);
        }

        private void CreateUrlsTab()
        {
            tabUrls = new TabPage("URLs Management");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            // Filter
            var lblFilter = new Label { Text = "Filter URLs:", Location = new Point(0, 10), AutoSize = true };
            txtFilterUrls = new TextBox 
            { 
                Location = new Point(0, 35), 
                Width = 300,
                Font = new Font("Segoe UI", 9F)
            };
            txtFilterUrls.TextChanged += TxtFilterUrls_TextChanged;
            
            // Buttons
            btnAddUrl = new Button 
            { 
                Text = "Add URL", 
                Location = new Point(320, 33), 
                Size = new Size(80, 25),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnAddUrl.Click += BtnAddUrl_Click;
            
            btnEditUrl = new Button 
            { 
                Text = "Edit", 
                Location = new Point(410, 33), 
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(241, 143, 1),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnEditUrl.Click += BtnEditUrl_Click;
            
            btnDeleteUrl = new Button 
            { 
                Text = "Delete", 
                Location = new Point(480, 33), 
                Size = new Size(60, 25),
                BackColor = Color.FromArgb(162, 59, 114),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnDeleteUrl.Click += BtnDeleteUrl_Click;
            
            btnClearUrls = new Button 
            { 
                Text = "Clear All", 
                Location = new Point(550, 33), 
                Size = new Size(70, 25),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnClearUrls.Click += BtnClearUrls_Click;
            
            // DataGridView
            dgvUrls = new DataGridView 
            { 
                Location = new Point(0, 70), 
                Size = new Size(950, 480),
                AutoGenerateColumns = false,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                Font = new Font("Segoe UI", 9F)
            };
            
            SetupUrlsDataGridView();
            
            panel.Controls.AddRange(new Control[] 
            { 
                lblFilter, txtFilterUrls, btnAddUrl, btnEditUrl, 
                btnDeleteUrl, btnClearUrls, dgvUrls 
            });
            
            tabUrls.Controls.Add(panel);
            tabControl.TabPages.Add(tabUrls);
        }

        private void CreateSettingsTab()
        {
            tabSettings = new TabPage("Settings");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            // Property grid for settings
            pgSettings = new PropertyGrid 
            { 
                Location = new Point(0, 10), 
                Size = new Size(950, 500),
                SelectedObject = _settings,
                Font = new Font("Segoe UI", 9F)
            };
            
            // Buttons
            btnSaveSettings = new Button 
            { 
                Text = "Save Settings", 
                Location = new Point(0, 520), 
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSaveSettings.Click += BtnSaveSettings_Click;
            
            btnResetSettings = new Button 
            { 
                Text = "Reset to Defaults", 
                Location = new Point(130, 520), 
                Size = new Size(120, 30),
                BackColor = Color.FromArgb(162, 59, 114),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnResetSettings.Click += BtnResetSettings_Click;
            
            panel.Controls.AddRange(new Control[] { pgSettings, btnSaveSettings, btnResetSettings });
            
            tabSettings.Controls.Add(panel);
            tabControl.TabPages.Add(tabSettings);
        }

        private void CreateGenerateTab()
        {
            tabGenerate = new TabPage("Generate Sitemap");
            
            var panel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };
            
            // Generate button
            btnGenerateSitemap = new Button 
            { 
                Text = "Generate Sitemap Files", 
                Location = new Point(0, 10), 
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Font = new Font("Segoe UI", 10F, FontStyle.Bold)
            };
            btnGenerateSitemap.Click += BtnGenerateSitemap_Click;
            
            // Progress
            progressGenerate = new ProgressBar 
            { 
                Location = new Point(0, 60), 
                Width = 950, 
                Height = 20 
            };
            
            lblGenerateStatus = new Label 
            { 
                Text = "Ready to generate sitemap", 
                Location = new Point(0, 90), 
                AutoSize = true,
                Font = new Font("Segoe UI", 9F, FontStyle.Bold)
            };
            
            // Log
            var lblLog = new Label { Text = "Generation Log:", Location = new Point(0, 120), AutoSize = true };
            txtGenerateLog = new RichTextBox 
            { 
                Location = new Point(0, 145), 
                Size = new Size(950, 400),
                ReadOnly = true,
                Font = new Font("Consolas", 9F),
                BackColor = Color.FromArgb(248, 248, 248)
            };
            
            panel.Controls.AddRange(new Control[] 
            { 
                btnGenerateSitemap, progressGenerate, lblGenerateStatus, lblLog, txtGenerateLog 
            });
            
            tabGenerate.Controls.Add(panel);
            tabControl.TabPages.Add(tabGenerate);
        }

        private void LoadSettings()
        {
            _settings = SitemapSettings.Load();
        }

        private void InitializeData()
        {
            _urls = new List<SitemapUrl>();
            _urlsBindingSource = new BindingSource { DataSource = _urls };
        }

        private void SetupUrlsDataGridView()
        {
            dgvUrls.DataSource = _urlsBindingSource;
            
            dgvUrls.Columns.Add(new DataGridViewCheckBoxColumn 
            { 
                DataPropertyName = "IsIncluded", 
                HeaderText = "Include", 
                Width = 60 
            });
            
            dgvUrls.Columns.Add(new DataGridViewTextBoxColumn 
            { 
                DataPropertyName = "Url", 
                HeaderText = "URL", 
                Width = 300 
            });
            
            dgvUrls.Columns.Add(new DataGridViewTextBoxColumn 
            { 
                DataPropertyName = "Title", 
                HeaderText = "Title", 
                Width = 200 
            });
            
            dgvUrls.Columns.Add(new DataGridViewComboBoxColumn 
            { 
                DataPropertyName = "ChangeFrequency", 
                HeaderText = "Change Frequency", 
                Width = 120,
                DataSource = Enum.GetValues(typeof(ChangeFrequency))
            });
            
            dgvUrls.Columns.Add(new DataGridViewTextBoxColumn 
            { 
                DataPropertyName = "Priority", 
                HeaderText = "Priority", 
                Width = 80 
            });
            
            dgvUrls.Columns.Add(new DataGridViewTextBoxColumn 
            { 
                DataPropertyName = "LastModified", 
                HeaderText = "Last Modified", 
                Width = 120 
            });
        }

        #region Event Handlers

        private async void BtnStartCrawl_Click(object? sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBaseUrl.Text))
            {
                MessageBox.Show("Please enter a valid website URL.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                _settings.BaseUrl = txtBaseUrl.Text.Trim();
                _settings.Save();

                btnStartCrawl.Enabled = false;
                btnStopCrawl.Enabled = true;
                progressCrawl.Style = ProgressBarStyle.Marquee;
                txtCrawlLog.Clear();

                _crawler = new WebCrawler(_settings);
                _crawler.ProgressChanged += Crawler_ProgressChanged;
                _crawler.StatusChanged += Crawler_StatusChanged;
                _crawler.UrlDiscovered += Crawler_UrlDiscovered;

                LogMessage("Starting website crawl...", Color.Blue);
                var discoveredUrls = await _crawler.CrawlAsync(_settings.BaseUrl);

                _urls.Clear();
                _urls.AddRange(discoveredUrls);
                _urlsBindingSource.ResetBindings(false);

                LogMessage($"Crawl completed! Discovered {discoveredUrls.Count} URLs.", Color.Green);
            }
            catch (Exception ex)
            {
                LogMessage($"Crawl error: {ex.Message}", Color.Red);
                MessageBox.Show($"Error during crawl: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnStartCrawl.Enabled = true;
                btnStopCrawl.Enabled = false;
                progressCrawl.Style = ProgressBarStyle.Continuous;
                progressCrawl.Value = 0;
            }
        }

        private void BtnStopCrawl_Click(object? sender, EventArgs e)
        {
            _crawler?.Stop();
        }

        private void Crawler_ProgressChanged(object? sender, CrawlProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => Crawler_ProgressChanged(sender, e)));
                return;
            }

            lblCrawlStatus.Text = $"Processed: {e.TotalProcessed}, Discovered: {e.TotalDiscovered}";
        }

        private void Crawler_StatusChanged(object? sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => Crawler_StatusChanged(sender, status)));
                return;
            }

            LogMessage(status, Color.Black);
        }

        private void Crawler_UrlDiscovered(object? sender, SitemapUrl url)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => Crawler_UrlDiscovered(sender, url)));
                return;
            }

            LogMessage($"Found: {url.Url}", Color.DarkGreen);
        }

        private void BtnAddUrl_Click(object? sender, EventArgs e)
        {
            using var dialog = new UrlEditDialog();
            if (dialog.ShowDialog() == DialogResult.OK && dialog.SitemapUrl != null)
            {
                _urls.Add(dialog.SitemapUrl);
                _urlsBindingSource.ResetBindings(false);
            }
        }

        private void BtnEditUrl_Click(object? sender, EventArgs e)
        {
            if (dgvUrls.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a URL to edit.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            var selectedUrl = (SitemapUrl)dgvUrls.SelectedRows[0].DataBoundItem;
            using var dialog = new UrlEditDialog(selectedUrl);
            if (dialog.ShowDialog() == DialogResult.OK)
            {
                _urlsBindingSource.ResetBindings(false);
            }
        }

        private void BtnDeleteUrl_Click(object? sender, EventArgs e)
        {
            if (dgvUrls.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select a URL to delete.", "No Selection", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (MessageBox.Show("Are you sure you want to delete the selected URL?", "Confirm Delete",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                var selectedUrl = (SitemapUrl)dgvUrls.SelectedRows[0].DataBoundItem;
                _urls.Remove(selectedUrl);
                _urlsBindingSource.ResetBindings(false);
            }
        }

        private void BtnClearUrls_Click(object? sender, EventArgs e)
        {
            if (_urls.Count == 0) return;

            if (MessageBox.Show("Are you sure you want to clear all URLs?", "Confirm Clear",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                _urls.Clear();
                _urlsBindingSource.ResetBindings(false);
            }
        }

        private void TxtFilterUrls_TextChanged(object? sender, EventArgs e)
        {
            var filter = txtFilterUrls.Text.ToLowerInvariant();
            if (string.IsNullOrWhiteSpace(filter))
            {
                _urlsBindingSource.RemoveFilter();
            }
            else
            {
                _urlsBindingSource.Filter = $"Url LIKE '%{filter}%' OR Title LIKE '%{filter}%'";
            }
        }

        private void BtnSaveSettings_Click(object? sender, EventArgs e)
        {
            try
            {
                _settings.Save();
                MessageBox.Show("Settings saved successfully!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnResetSettings_Click(object? sender, EventArgs e)
        {
            if (MessageBox.Show("Are you sure you want to reset all settings to defaults?", "Confirm Reset",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                SitemapSettings.ResetToDefaults();
                _settings = SitemapSettings.Load();
                pgSettings.SelectedObject = _settings;
                MessageBox.Show("Settings reset to defaults!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void BtnGenerateSitemap_Click(object? sender, EventArgs e)
        {
            if (_urls.Count == 0)
            {
                MessageBox.Show("No URLs to generate sitemap. Please crawl a website or add URLs manually.",
                    "No URLs", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnGenerateSitemap.Enabled = false;
                progressGenerate.Style = ProgressBarStyle.Marquee;
                txtGenerateLog.Clear();

                LogGenerateMessage("Starting sitemap generation...", Color.Blue);

                var builder = new SitemapBuilder(_settings);
                var result = await builder.GenerateSitemapsAsync(_urls);

                if (result.Success)
                {
                    LogGenerateMessage($"✅ {result.Message}", Color.Green);
                    LogGenerateMessage($"Generated files:", Color.Black);

                    foreach (var file in result.GeneratedFiles)
                    {
                        LogGenerateMessage($"  • {file}", Color.DarkGreen);
                    }

                    if (!string.IsNullOrEmpty(result.IndexFile))
                    {
                        LogGenerateMessage($"  • {result.IndexFile} (index)", Color.DarkGreen);
                    }

                    if (!string.IsNullOrEmpty(result.RobotsFile))
                    {
                        LogGenerateMessage($"  • {result.RobotsFile}", Color.DarkGreen);
                    }

                    MessageBox.Show($"Sitemap generated successfully!\n\nFiles created: {result.GeneratedFiles.Count}\nTotal URLs: {result.TotalUrls}",
                        "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    LogGenerateMessage($"❌ {result.Message}", Color.Red);
                    MessageBox.Show($"Error generating sitemap: {result.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LogGenerateMessage($"❌ Error: {ex.Message}", Color.Red);
                MessageBox.Show($"Error generating sitemap: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnGenerateSitemap.Enabled = true;
                progressGenerate.Style = ProgressBarStyle.Continuous;
                progressGenerate.Value = 0;
            }
        }

        #endregion

        #region Helper Methods

        private void LogMessage(string message, Color color)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => LogMessage(message, color)));
                return;
            }

            txtCrawlLog.SelectionStart = txtCrawlLog.TextLength;
            txtCrawlLog.SelectionLength = 0;
            txtCrawlLog.SelectionColor = color;
            txtCrawlLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\n");
            txtCrawlLog.SelectionColor = txtCrawlLog.ForeColor;
            txtCrawlLog.ScrollToCaret();
        }

        private void LogGenerateMessage(string message, Color color)
        {
            txtGenerateLog.SelectionStart = txtGenerateLog.TextLength;
            txtGenerateLog.SelectionLength = 0;
            txtGenerateLog.SelectionColor = color;
            txtGenerateLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\n");
            txtGenerateLog.SelectionColor = txtGenerateLog.ForeColor;
            txtGenerateLog.ScrollToCaret();
        }

        private void NewProject()
        {
            if (MessageBox.Show("Create a new project? This will clear all current URLs.", "New Project",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                _urls.Clear();
                _urlsBindingSource.ResetBindings(false);
                txtCrawlLog.Clear();
                txtGenerateLog.Clear();
            }
        }

        private void OpenProject()
        {
            // Implementation for opening saved projects
            MessageBox.Show("Project loading feature coming soon!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void SaveProject()
        {
            // Implementation for saving projects
            MessageBox.Show("Project saving feature coming soon!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ValidateSitemap()
        {
            // Implementation for sitemap validation
            MessageBox.Show("Sitemap validation feature coming soon!", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void OpenOutputFolder()
        {
            try
            {
                if (System.IO.Directory.Exists(_settings.OutputDirectory))
                {
                    System.Diagnostics.Process.Start("explorer.exe", _settings.OutputDirectory);
                }
                else
                {
                    MessageBox.Show("Output directory does not exist yet. Generate a sitemap first.", "Directory Not Found",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening output folder: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAbout()
        {
            MessageBox.Show("SEO Sitemap Generator v1.0\n\nDeveloped by HokTech\n\nA comprehensive tool for generating Google-compliant XML sitemaps with full SEO support.",
                "About", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void ShowUserGuide()
        {
            MessageBox.Show("User Guide:\n\n1. Enter your website URL in the Crawl tab\n2. Click 'Start Crawl' to discover URLs\n3. Review and edit URLs in the URLs tab\n4. Configure settings in the Settings tab\n5. Generate sitemaps in the Generate tab",
                "User Guide", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            _crawler?.Stop();
            _crawler?.Dispose();
            base.OnFormClosing(e);
        }
    }
}
