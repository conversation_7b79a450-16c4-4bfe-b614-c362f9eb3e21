using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using HokTechPOS.Models;
using HokTechPOS.Services;

namespace HokTechPOS
{
    public partial class MainForm : Form
    {
        private readonly List<Product> _products = new List<Product>();
        private readonly InvoiceGenerator _invoiceGenerator = new InvoiceGenerator();
        
        // UI Controls
        private TextBox _productNameTextBox = null!;
        private NumericUpDown _quantityNumericUpDown = null!;
        private TextBox _priceTextBox = null!;
        private Button _addButton = null!;
        private DataGridView _productsDataGridView = null!;
        private TextBox _customerNameTextBox = null!;
        private TextBox _notesTextBox = null!;
        private Label _totalLabel = null!;
        private Button _deleteButton = null!;
        private Button _clearButton = null!;
        private Button _generateInvoiceButton = null!;
        
        public MainForm()
        {
            InitializeComponent();
            SetupEventHandlers();
            UpdateTotal();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "HokTech POS System";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);
            this.BackColor = Color.FromArgb(240, 240, 240);
            
            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = Color.FromArgb(46, 134, 171),
                Padding = new Padding(20)
            };
            
            var titleLabel = new Label
            {
                Text = "HokTech POS System\nPoint of Sale",
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Dock = DockStyle.Fill
            };
            headerPanel.Controls.Add(titleLabel);
            this.Controls.Add(headerPanel);
            
            // Main content panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };
            
            // Product input group
            var productGroup = new GroupBox
            {
                Text = "Add Product",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Height = 120,
                Dock = DockStyle.Top,
                Padding = new Padding(10),
                Margin = new Padding(0, 10, 0, 10)
            };
            
            // Product name
            var nameLabel = new Label
            {
                Text = "Product Name:",
                Location = new Point(20, 30),
                Size = new Size(100, 23),
                Font = new Font("Arial", 10)
            };
            productGroup.Controls.Add(nameLabel);
            
            _productNameTextBox = new TextBox
            {
                Location = new Point(130, 27),
                Size = new Size(250, 23),
                Font = new Font("Arial", 10)
            };
            productGroup.Controls.Add(_productNameTextBox);
            
            // Quantity
            var quantityLabel = new Label
            {
                Text = "Quantity:",
                Location = new Point(400, 30),
                Size = new Size(60, 23),
                Font = new Font("Arial", 10)
            };
            productGroup.Controls.Add(quantityLabel);
            
            _quantityNumericUpDown = new NumericUpDown
            {
                Location = new Point(470, 27),
                Size = new Size(80, 23),
                Font = new Font("Arial", 10),
                Minimum = 1,
                Maximum = 1000,
                Value = 1
            };
            productGroup.Controls.Add(_quantityNumericUpDown);
            
            // Price
            var priceLabel = new Label
            {
                Text = "Price (SAR):",
                Location = new Point(570, 30),
                Size = new Size(80, 23),
                Font = new Font("Arial", 10)
            };
            productGroup.Controls.Add(priceLabel);
            
            _priceTextBox = new TextBox
            {
                Location = new Point(660, 27),
                Size = new Size(100, 23),
                Font = new Font("Arial", 10)
            };
            productGroup.Controls.Add(_priceTextBox);
            
            // Add button
            _addButton = new Button
            {
                Text = "Add Product",
                Location = new Point(780, 25),
                Size = new Size(120, 30),
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(241, 143, 1),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _addButton.FlatAppearance.BorderSize = 0;
            productGroup.Controls.Add(_addButton);
            
            mainPanel.Controls.Add(productGroup);
            
            // Products table group
            var tableGroup = new GroupBox
            {
                Text = "Products List",
                Font = new Font("Arial", 12, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                Margin = new Padding(0, 10, 0, 0)
            };
            
            _productsDataGridView = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                ReadOnly = true,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(46, 134, 171),
                    ForeColor = Color.White,
                    Font = new Font("Arial", 10, FontStyle.Bold)
                }
            };
            
            // Setup columns
            _productsDataGridView.Columns.Add("Product", "Product");
            _productsDataGridView.Columns.Add("Quantity", "Quantity");
            _productsDataGridView.Columns.Add("Price", "Price (SAR)");
            _productsDataGridView.Columns.Add("Total", "Total (SAR)");
            
            _productsDataGridView.Columns[0].Width = 300;
            _productsDataGridView.Columns[1].Width = 100;
            _productsDataGridView.Columns[2].Width = 120;
            _productsDataGridView.Columns[3].Width = 120;
            
            // Right align numeric columns
            _productsDataGridView.Columns[1].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _productsDataGridView.Columns[2].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _productsDataGridView.Columns[3].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            
            tableGroup.Controls.Add(_productsDataGridView);
            mainPanel.Controls.Add(tableGroup);

            // Bottom panel (separate from mainPanel)
            var bottomPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 200,
                Padding = new Padding(0, 10, 0, 0)
            };
            
            // Customer info panel
            var customerPanel = new GroupBox
            {
                Text = "Customer Information",
                Font = new Font("Arial", 10, FontStyle.Bold),
                Dock = DockStyle.Left,
                Width = 400,
                Padding = new Padding(10)
            };
            
            var customerLabel = new Label
            {
                Text = "Customer Name:",
                Location = new Point(15, 30),
                Size = new Size(100, 23),
                Font = new Font("Arial", 9)
            };
            customerPanel.Controls.Add(customerLabel);
            
            _customerNameTextBox = new TextBox
            {
                Location = new Point(15, 55),
                Size = new Size(360, 23),
                Font = new Font("Arial", 9)
            };
            customerPanel.Controls.Add(_customerNameTextBox);
            
            var notesLabel = new Label
            {
                Text = "Notes:",
                Location = new Point(15, 90),
                Size = new Size(50, 23),
                Font = new Font("Arial", 9)
            };
            customerPanel.Controls.Add(notesLabel);
            
            _notesTextBox = new TextBox
            {
                Location = new Point(15, 115),
                Size = new Size(360, 60),
                Font = new Font("Arial", 9),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            customerPanel.Controls.Add(_notesTextBox);
            
            bottomPanel.Controls.Add(customerPanel);
            
            // Actions panel
            var actionsPanel = new Panel
            {
                Dock = DockStyle.Right,
                Width = 300,
                Padding = new Padding(20, 0, 0, 0)
            };
            
            // Total label
            _totalLabel = new Label
            {
                Text = "Total: 0.00 SAR",
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 134, 171),
                Location = new Point(30, 20),
                Size = new Size(250, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };
            actionsPanel.Controls.Add(_totalLabel);
            
            // Buttons
            _deleteButton = new Button
            {
                Text = "Delete\nItem",
                Location = new Point(30, 70),
                Size = new Size(80, 50),
                Font = new Font("Arial", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _deleteButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_deleteButton);
            
            _clearButton = new Button
            {
                Text = "Clear\nAll",
                Location = new Point(120, 70),
                Size = new Size(80, 50),
                Font = new Font("Arial", 9, FontStyle.Bold),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _clearButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_clearButton);
            
            _generateInvoiceButton = new Button
            {
                Text = "Generate\nInvoice",
                Location = new Point(30, 130),
                Size = new Size(170, 50),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 134, 171),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            _generateInvoiceButton.FlatAppearance.BorderSize = 0;
            actionsPanel.Controls.Add(_generateInvoiceButton);
            
            bottomPanel.Controls.Add(actionsPanel);

            // Add panels to form in correct order
            this.Controls.Add(bottomPanel);  // Add bottom panel first
            this.Controls.Add(mainPanel);    // Then main panel
            this.ResumeLayout(false);
        }
        
        private void SetupEventHandlers()
        {
            _addButton.Click += AddButton_Click;
            _deleteButton.Click += DeleteButton_Click;
            _clearButton.Click += ClearButton_Click;
            _generateInvoiceButton.Click += GenerateInvoiceButton_Click;
            
            // Enter key navigation
            _productNameTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _quantityNumericUpDown.Focus(); };
            _quantityNumericUpDown.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) _priceTextBox.Focus(); };
            _priceTextBox.KeyDown += (s, e) => { if (e.KeyCode == Keys.Enter) AddProduct(); };
        }
        
        private void AddButton_Click(object? sender, EventArgs e)
        {
            AddProduct();
        }
        
        private void AddProduct()
        {
            var name = _productNameTextBox.Text.Trim();
            var quantity = (int)_quantityNumericUpDown.Value;
            
            if (string.IsNullOrWhiteSpace(name))
            {
                MessageBox.Show("Please enter product name.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _productNameTextBox.Focus();
                return;
            }
            
            if (!decimal.TryParse(_priceTextBox.Text, out decimal price) || price <= 0)
            {
                MessageBox.Show("Please enter a valid price.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                _priceTextBox.Focus();
                return;
            }
            
            var product = new Product(name, quantity, price);
            _products.Add(product);
            
            // Add to grid
            _productsDataGridView.Rows.Add(product.Name, product.Quantity, $"{product.Price:F2}", $"{product.Total:F2}");
            
            // Clear inputs
            _productNameTextBox.Clear();
            _quantityNumericUpDown.Value = 1;
            _priceTextBox.Clear();
            _productNameTextBox.Focus();
            
            UpdateTotal();
        }
        
        private void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (_productsDataGridView.SelectedRows.Count == 0)
            {
                MessageBox.Show("Please select an item to delete.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedIndex = _productsDataGridView.SelectedRows[0].Index;
            _products.RemoveAt(selectedIndex);
            _productsDataGridView.Rows.RemoveAt(selectedIndex);

            UpdateTotal();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0) return;

            var result = MessageBox.Show("Do you want to clear all items?", "Confirm",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                _products.Clear();
                _productsDataGridView.Rows.Clear();
                UpdateTotal();
            }
        }

        private void GenerateInvoiceButton_Click(object? sender, EventArgs e)
        {
            if (_products.Count == 0)
            {
                MessageBox.Show("Please add products first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            try
            {
                var invoiceNumber = _invoiceGenerator.GetNextInvoiceNumber();
                var invoice = new Invoice(invoiceNumber, _customerNameTextBox.Text.Trim(), _notesTextBox.Text.Trim());
                
                foreach (var product in _products)
                {
                    invoice.AddProduct(product);
                }
                
                var filePath = _invoiceGenerator.GenerateInvoice(invoice);
                
                MessageBox.Show($"Invoice created successfully!\n\nFile: {System.IO.Path.GetFileName(filePath)}", 
                    "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                var openResult = MessageBox.Show("Do you want to open the invoice?", "Open Invoice", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (openResult == DialogResult.Yes)
                {
                    _invoiceGenerator.OpenInvoice(filePath);
                }
                
                var clearResult = MessageBox.Show("Do you want to clear data for a new invoice?", "Clear Data", 
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (clearResult == DialogResult.Yes)
                {
                    _products.Clear();
                    _productsDataGridView.Rows.Clear();
                    _customerNameTextBox.Clear();
                    _notesTextBox.Clear();
                    UpdateTotal();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating invoice:\n{ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void UpdateTotal()
        {
            var total = _products.Sum(p => p.Total);
            _totalLabel.Text = $"Total: {total:F2} SAR";
        }
    }
}
